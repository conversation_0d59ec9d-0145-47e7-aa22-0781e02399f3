export const config = {
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost'
  },
  
  vault: {
    defaultVaultId: '117',
    defaultVaultName: 'TestVault',
    apiVersion: 'v2',
    basePath: '/AutodeskDM/Services/api/vault'
  },
  
  auth: {
    tokenPrefix: 'AuIPTf4KYLTYGVnOHQ0cuolwCW2a',
    defaultUser: 'TestAccount'
  },
  
  pagination: {
    defaultLimit: 10,
    maxLimit: 100
  },
  
  mockData: {
    generateBulkData: true,
    fileVersionCount: 50,
    folderCount: 10,
    enableRandomData: true
  },
  
  features: {
    enableCORS: true,
    enableHelmet: true,
    enableLogging: true,
    enableRateLimiting: false
  }
};

export default config;
