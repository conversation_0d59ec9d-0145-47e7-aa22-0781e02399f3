import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import config from './config.js';
import vaultRoutes from './routes/vaultRoutes.js';
import { MockDataGenerator } from './data/mockDataGenerator.js';

const app = express();
const PORT = config.server.port;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Store mock data in app locals for access across routes
app.locals.mockData = {
  vaults: new Map(),
  files: new Map(),
  fileVersions: new Map(),
  folders: new Map(),
  users: new Map()
};

// Initialize mock data
initializeMockData();

// Authentication middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      error: 'Access token required',
      message: 'Please provide a Bearer token in the Authorization header'
    });
  }

  // Mock token validation - in real implementation, verify JWT
  if (!token.startsWith(config.auth.tokenPrefix)) {
    return res.status(403).json({
      error: 'Invalid token',
      message: 'Token must start with the correct prefix'
    });
  }

  req.user = { id: config.auth.defaultUser, name: config.auth.defaultUser };
  next();
}

// Routes

// Health check
app.get('/health', (req, res) => {
  const stats = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime(),
    mockData: {
      vaults: app.locals.mockData.vaults.size,
      files: app.locals.mockData.files.size,
      fileVersions: app.locals.mockData.fileVersions.size,
      folders: app.locals.mockData.folders.size
    }
  };
  res.json(stats);
});

// API Documentation endpoint
app.get('/api-docs', (req, res) => {
  const docs = {
    title: 'Vault Mock API',
    version: '1.0.0',
    description: 'Mock API server for Autodesk Vault',
    baseUrl: `http://${config.server.host}:${config.server.port}${config.vault.basePath}/${config.vault.apiVersion}`,
    endpoints: {
      'GET /health': 'Health check and server statistics',
      'GET /vaults': 'List all vaults',
      'GET /vaults/{vaultId}': 'Get specific vault',
      'GET /vaults/{vaultId}/file-versions': 'List file versions with filtering and pagination',
      'GET /vaults/{vaultId}/file-versions/{fileVersionId}': 'Get specific file version',
      'GET /vaults/{vaultId}/files': 'List files',
      'GET /vaults/{vaultId}/files/{fileId}': 'Get specific file',
      'GET /vaults/{vaultId}/folders': 'List folders',
      'GET /vaults/{vaultId}/folders/{folderId}': 'Get specific folder'
    },
    authentication: {
      type: 'Bearer Token',
      header: 'Authorization: Bearer {token}',
      tokenPrefix: config.auth.tokenPrefix
    },
    sampleRequests: {
      fileVersions: `curl -H "Authorization: Bearer ${config.auth.tokenPrefix}..." "${req.protocol}://${req.get('host')}${config.vault.basePath}/${config.vault.apiVersion}/vaults/${config.vault.defaultVaultId}/file-versions?limit=2"`
    }
  };
  res.json(docs);
});

// Mount vault routes with authentication
app.use(`${config.vault.basePath}/${config.vault.apiVersion}`, authenticateToken, vaultRoutes);





// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Vault Mock API server running on port ${PORT}`);
  console.log(`📖 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 API Base URL: http://localhost:${PORT}/AutodeskDM/Services/api/vault/v2`);
});

function initializeMockData() {
  const mockData = app.locals.mockData;

  // Create mock vault
  const vault = MockDataGenerator.generateVault({
    id: config.vault.defaultVaultId,
    name: config.vault.defaultVaultName
  });
  mockData.vaults.set(config.vault.defaultVaultId, vault);

  // Create base folders
  const baseFolder = MockDataGenerator.generateFolder({
    id: "2",
    vaultId: config.vault.defaultVaultId,
    name: "ColinTest"
  });
  mockData.folders.set("2", baseFolder);

  // Generate additional folders if enabled
  if (config.mockData.generateBulkData) {
    const additionalFolders = MockDataGenerator.generateBulkFolders(
      config.mockData.folderCount,
      config.vault.defaultVaultId
    );
    additionalFolders.forEach(folder => {
      mockData.folders.set(folder.id, folder);
    });
  }

  // Create base files
  const file1 = MockDataGenerator.generateFile({
    id: "37",
    vaultId: config.vault.defaultVaultId,
    name: "Assembly2.iam"
  });
  mockData.files.set("37", file1);

  const file2 = MockDataGenerator.generateFile({
    id: "31",
    vaultId: config.vault.defaultVaultId,
    name: "Test1.ipt"
  });
  mockData.files.set("31", file2);

  // Create base file versions (matching the original example)
  const fileVersion1 = {
    name: "Assembly2.iam",
    id: "43",
    vaultId: config.vault.defaultVaultId,
    state: "",
    stateColor: 0,
    revision: "",
    category: "Base",
    categoryColor: -2302756,
    lastModifiedDate: "2024-10-17T19:17:40.043Z",
    isCheckedOut: true,
    hasVisualizationAttachment: true,
    size: 237568,
    isCloaked: false,
    checkinDate: "0001-01-01T00:00:00Z",
    checkoutDate: "2024-10-17T19:23:18.383Z",
    checkoutUserName: config.auth.defaultUser,
    isHidden: false,
    isReadOnly: false,
    parentFolderId: "2",
    file: {
      id: "37",
      entityType: "File",
      url: `${config.vault.basePath}/${config.vault.apiVersion}/vaults/${config.vault.defaultVaultId}/files/37`
    },
    isOnSite: true,
    createDate: "2024-10-17T19:17:49.8Z",
    createUserName: config.auth.defaultUser,
    classification: "None",
    visualizationAttachmentStatus: "NotSyncronized",
    version: 3,
    entityType: "FileVersion",
    url: `${config.vault.basePath}/${config.vault.apiVersion}/vaults/${config.vault.defaultVaultId}/file-versions/43`
  };
  mockData.fileVersions.set("43", fileVersion1);

  const fileVersion2 = {
    name: "Test1.ipt",
    id: "40",
    vaultId: config.vault.defaultVaultId,
    state: "",
    stateColor: 0,
    revision: "",
    category: "Base",
    categoryColor: -2302756,
    lastModifiedDate: "2024-10-17T18:46:33.497Z",
    isCheckedOut: false,
    hasVisualizationAttachment: true,
    size: 209920,
    isCloaked: false,
    checkinDate: "2024-10-17T19:17:46.297Z",
    checkoutDate: "2024-10-17T18:45:51.907Z",
    isHidden: false,
    isReadOnly: false,
    parentFolderId: "2",
    file: {
      id: "31",
      entityType: "File",
      url: `${config.vault.basePath}/${config.vault.apiVersion}/vaults/${config.vault.defaultVaultId}/files/31`
    },
    isOnSite: true,
    createDate: "2024-10-17T19:17:46.297Z",
    createUserName: config.auth.defaultUser,
    classification: "None",
    visualizationAttachmentStatus: "NotSyncronized",
    version: 2,
    entityType: "FileVersion",
    url: `${config.vault.basePath}/${config.vault.apiVersion}/vaults/${config.vault.defaultVaultId}/file-versions/40`
  };
  mockData.fileVersions.set("40", fileVersion2);

  // Generate additional file versions if enabled
  if (config.mockData.generateBulkData) {
    const additionalFileVersions = MockDataGenerator.generateBulkFileVersions(
      config.mockData.fileVersionCount,
      config.vault.defaultVaultId
    );

    additionalFileVersions.forEach(fileVersion => {
      mockData.fileVersions.set(fileVersion.id, fileVersion);

      // Also create corresponding file entries
      if (!mockData.files.has(fileVersion.file.id)) {
        const file = MockDataGenerator.generateFile({
          id: fileVersion.file.id,
          vaultId: config.vault.defaultVaultId,
          name: fileVersion.name
        });
        mockData.files.set(fileVersion.file.id, file);
      }
    });
  }

  console.log(`✅ Mock data initialized:`);
  console.log(`   📁 ${mockData.vaults.size} vaults`);
  console.log(`   📂 ${mockData.folders.size} folders`);
  console.log(`   📄 ${mockData.files.size} files`);
  console.log(`   📋 ${mockData.fileVersions.size} file versions`);
}
