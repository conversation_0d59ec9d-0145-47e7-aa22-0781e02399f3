import { v4 as uuidv4 } from 'uuid';

export class MockDataGenerator {
  static generateFileVersion(options = {}) {
    const id = options.id || Math.floor(Math.random() * 1000).toString();
    const vaultId = options.vaultId || "117";
    const parentFolderId = options.parentFolderId || "2";
    
    const fileExtensions = ['.iam', '.ipt', '.idw', '.dwg', '.pdf', '.txt', '.xlsx'];
    const categories = ['Base', 'Design', 'Manufacturing', 'Documentation'];
    const states = ['', 'Work in Progress', 'For Review', 'Released'];
    
    const extension = options.extension || fileExtensions[Math.floor(Math.random() * fileExtensions.length)];
    const baseName = options.name || `MockFile${id}`;
    const fileName = baseName.includes('.') ? baseName : `${baseName}${extension}`;
    
    const category = categories[Math.floor(Math.random() * categories.length)];
    const state = states[Math.floor(Math.random() * states.length)];
    const isCheckedOut = Math.random() > 0.7;
    const version = Math.floor(Math.random() * 10) + 1;
    
    const createDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
    const lastModifiedDate = new Date(createDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
    
    return {
      name: fileName,
      id: id,
      vaultId: vaultId,
      state: state,
      stateColor: state ? Math.floor(Math.random() * 16777215) : 0,
      revision: version > 1 ? String.fromCharCode(64 + version) : "",
      category: category,
      categoryColor: -2302756 + Math.floor(Math.random() * 1000000),
      lastModifiedDate: lastModifiedDate.toISOString(),
      isCheckedOut: isCheckedOut,
      hasVisualizationAttachment: Math.random() > 0.3,
      size: Math.floor(Math.random() * 1000000) + 10000,
      isCloaked: false,
      checkinDate: isCheckedOut ? "0001-01-01T00:00:00Z" : lastModifiedDate.toISOString(),
      checkoutDate: isCheckedOut ? new Date().toISOString() : lastModifiedDate.toISOString(),
      checkoutUserName: isCheckedOut ? "TestAccount" : "",
      isHidden: false,
      isReadOnly: Math.random() > 0.9,
      parentFolderId: parentFolderId,
      file: {
        id: (parseInt(id) - 10).toString(),
        entityType: "File",
        url: `/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/files/${parseInt(id) - 10}`
      },
      isOnSite: true,
      createDate: createDate.toISOString(),
      createUserName: "TestAccount",
      classification: Math.random() > 0.8 ? "Confidential" : "None",
      visualizationAttachmentStatus: Math.random() > 0.5 ? "Synchronized" : "NotSyncronized",
      version: version,
      entityType: "FileVersion",
      url: `/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/file-versions/${id}`
    };
  }

  static generateFolder(options = {}) {
    const id = options.id || Math.floor(Math.random() * 1000).toString();
    const vaultId = options.vaultId || "117";
    const name = options.name || `Folder${id}`;
    const parentPath = options.parentPath || "$";
    
    const createDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
    
    return {
      name: name,
      id: id,
      vaultId: vaultId,
      fullName: `${parentPath}/${name}`,
      category: "Folder",
      categoryColor: -2302756,
      stateColor: 0,
      subfolderCount: Math.floor(Math.random() * 5),
      isLibrary: Math.random() > 0.9,
      isCloaked: false,
      isReadOnly: Math.random() > 0.95,
      createDate: createDate.toISOString(),
      createUserName: "TestAccount",
      entityType: "Folder",
      url: `/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/folders/${id}`
    };
  }

  static generateFile(options = {}) {
    const id = options.id || Math.floor(Math.random() * 1000).toString();
    const vaultId = options.vaultId || "117";
    const name = options.name || `File${id}.ipt`;
    
    return {
      id: id,
      vaultId: vaultId,
      name: name,
      entityType: "File",
      url: `/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/files/${id}`
    };
  }

  static generateVault(options = {}) {
    const id = options.id || Math.floor(Math.random() * 1000).toString();
    const name = options.name || `Vault${id}`;
    
    return {
      id: id,
      name: name,
      description: `Mock vault ${name} for testing purposes`,
      entityType: "Vault",
      url: `/AutodeskDM/Services/api/vault/v2/vaults/${id}`
    };
  }

  static generateBulkFileVersions(count = 50, vaultId = "117") {
    const fileVersions = [];
    const folderIds = ["2", "3", "4", "5"];
    
    for (let i = 0; i < count; i++) {
      const id = (100 + i).toString();
      const parentFolderId = folderIds[Math.floor(Math.random() * folderIds.length)];
      
      fileVersions.push(this.generateFileVersion({
        id: id,
        vaultId: vaultId,
        parentFolderId: parentFolderId
      }));
    }
    
    return fileVersions;
  }

  static generateBulkFolders(count = 10, vaultId = "117") {
    const folders = [];
    const projectNames = ["Engineering", "Manufacturing", "Quality", "Documentation", "Archive"];
    
    for (let i = 0; i < count; i++) {
      const id = (10 + i).toString();
      const projectName = projectNames[Math.floor(Math.random() * projectNames.length)];
      const name = `${projectName}_${i + 1}`;
      
      folders.push(this.generateFolder({
        id: id,
        vaultId: vaultId,
        name: name
      }));
    }
    
    return folders;
  }
}

export default MockDataGenerator;
