# Vault Mock API

A comprehensive mock API server that simulates the Autodesk Vault API for development and testing purposes.

## 🚀 Features

- **Complete Vault API Simulation**: Mimics Autodesk Vault REST API endpoints
- **Realistic Mock Data**: Generates realistic file versions, folders, and vault structures
- **Authentication**: Bearer token authentication simulation
- **Pagination**: Cursor-based pagination with configurable limits
- **Filtering & Sorting**: Advanced filtering and sorting capabilities
- **Bulk Data Generation**: Configurable bulk data generation for testing
- **CORS Support**: Cross-origin resource sharing enabled
- **API Documentation**: Built-in API documentation endpoint

## 📁 Project Structure

```
VaultMockAPI/
├── index.js                 # Main server file
├── config.js               # Configuration settings
├── routes/
│   └── vaultRoutes.js      # Vault API route handlers
├── data/
│   └── mockDataGenerator.js # Mock data generation utilities
├── package.json
└── README.md
```

## 🛠️ Installation & Setup

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Start the Server**:
   ```bash
   npm start
   ```

3. **Development Mode** (with auto-restart):
   ```bash
   npm run dev
   ```

The server will start on `http://localhost:3000` by default.

## 📖 API Documentation

### Base URL
```
http://localhost:3000/AutodeskDM/Services/api/vault/v2
```

### Authentication
All API endpoints require a Bearer token in the Authorization header:
```bash
Authorization: Bearer AuIPTf4KYLTYGVnOHQ0cuolwCW2a...
```

### Available Endpoints

#### Health & Documentation
- `GET /health` - Server health check and statistics
- `GET /api-docs` - API documentation and examples

#### Vaults
- `GET /vaults` - List all vaults
- `GET /vaults/{vaultId}` - Get specific vault

#### File Versions
- `GET /vaults/{vaultId}/file-versions` - List file versions with filtering
- `GET /vaults/{vaultId}/file-versions/{fileVersionId}` - Get specific file version

#### Files
- `GET /vaults/{vaultId}/files` - List files
- `GET /vaults/{vaultId}/files/{fileId}` - Get specific file

#### Folders
- `GET /vaults/{vaultId}/folders` - List folders
- `GET /vaults/{vaultId}/folders/{folderId}` - Get specific folder

## 🔍 Query Parameters

### File Versions Endpoint
- `limit` - Number of results per page (default: 10, max: 100)
- `cursorState` - Base64 encoded cursor for pagination
- `filter` - Text filter for name, category, or state
- `category` - Filter by category
- `state` - Filter by state
- `isCheckedOut` - Filter by checkout status (true/false)
- `orderBy` - Sort field (default: lastModifiedDate)
- `orderDirection` - Sort direction: asc/desc (default: desc)

### Example Requests

#### Get File Versions (Basic)
```bash
curl -H "Authorization: Bearer AuIPTf4KYLTYGVnOHQ0cuolwCW2a..." \
  "http://localhost:3000/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions?limit=2"
```

#### Get File Versions (With Filtering)
```bash
curl -H "Authorization: Bearer AuIPTf4KYLTYGVnOHQ0cuolwCW2a..." \
  "http://localhost:3000/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions?limit=5&category=Base&isCheckedOut=true"
```

#### Get Health Status
```bash
curl "http://localhost:3000/health"
```

## 📊 Response Format

### File Versions Response
```json
{
  "pagination": {
    "limit": 2,
    "totalResults": 52,
    "nextUrl": "/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions?limit=2&cursorState=...",
    "indexingStatus": "IndexingComplete"
  },
  "results": [
    {
      "name": "Assembly2.iam",
      "id": "43",
      "state": "",
      "stateColor": 0,
      "revision": "",
      "category": "Base",
      "categoryColor": -2302756,
      "lastModifiedDate": "2024-10-17T19:17:40.043Z",
      "isCheckedOut": true,
      "hasVisualizationAttachment": true,
      "size": 237568,
      "isCloaked": false,
      "checkinDate": "0001-01-01T00:00:00Z",
      "checkoutDate": "2024-10-17T19:23:18.383Z",
      "checkoutUserName": "TestAccount",
      "isHidden": false,
      "isReadOnly": false,
      "parentFolderId": "2",
      "file": {
        "id": "37",
        "entityType": "File",
        "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/files/37"
      },
      "isOnSite": true,
      "createDate": "2024-10-17T19:17:49.8Z",
      "createUserName": "TestAccount",
      "classification": "None",
      "visualizationAttachmentStatus": "NotSyncronized",
      "version": 3,
      "entityType": "FileVersion",
      "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/file-versions/43"
    }
  ],
  "included": {
    "folder": {
      "2": {
        "name": "ColinTest",
        "id": "2",
        "fullName": "$/ColinTest",
        "category": "Folder",
        "categoryColor": -2302756,
        "stateColor": 0,
        "subfolderCount": 0,
        "isLibrary": false,
        "isCloaked": false,
        "isReadOnly": false,
        "createDate": "2024-10-11T07:04:11.723Z",
        "createUserName": "TestAccount",
        "entityType": "Folder",
        "url": "/AutodeskDM/Services/api/vault/v2/vaults/117/folders/2"
      }
    }
  }
}
```

## ⚙️ Configuration

Edit `config.js` to customize:

- **Server settings** (port, host)
- **Mock data generation** (bulk data, counts)
- **Authentication** (token prefix, default user)
- **Pagination** (default/max limits)
- **Features** (CORS, logging, etc.)

## 🧪 Testing

The mock API includes realistic test data:
- Default vault (ID: 117)
- Sample folders and files
- Configurable bulk data generation
- Various file states and categories

## 🔧 Development

### Adding New Endpoints
1. Add route handlers in `routes/vaultRoutes.js`
2. Update mock data generators if needed
3. Test with curl or API client

### Customizing Mock Data
- Modify `data/mockDataGenerator.js` for custom data patterns
- Adjust `config.js` for bulk data settings
- Add new entity types as needed

## 📝 Notes

- This is a mock API for development/testing only
- No actual file storage or persistence
- Data is regenerated on each server restart
- Authentication is simulated (any token with correct prefix works)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.
