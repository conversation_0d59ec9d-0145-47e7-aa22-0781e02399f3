import express from 'express';
import { MockDataGenerator } from '../data/mockDataGenerator.js';

const router = express.Router();

// Middleware to get vault data
function getVaultData(req, res, next) {
  req.vaultData = req.app.locals.mockData;
  next();
}

router.use(getVaultData);

// Get all vaults
router.get('/vaults', (req, res) => {
  const vaults = Array.from(req.vaultData.vaults.values());
  res.json({
    results: vaults,
    pagination: {
      limit: vaults.length,
      totalResults: vaults.length,
      indexingStatus: "IndexingComplete"
    }
  });
});

// Get specific vault
router.get('/vaults/:vaultId', (req, res) => {
  const { vaultId } = req.params;
  const vault = req.vaultData.vaults.get(vaultId);
  
  if (!vault) {
    return res.status(404).json({ error: 'Vault not found' });
  }
  
  res.json(vault);
});

// Get file versions with advanced filtering
router.get('/vaults/:vaultId/file-versions', (req, res) => {
  const { vaultId } = req.params;
  const { 
    limit = 10, 
    cursorState, 
    filter, 
    orderBy = 'lastModifiedDate',
    orderDirection = 'desc',
    category,
    state,
    isCheckedOut
  } = req.query;
  
  const vault = req.vaultData.vaults.get(vaultId);
  if (!vault) {
    return res.status(404).json({ error: 'Vault not found' });
  }

  let allFileVersions = Array.from(req.vaultData.fileVersions.values())
    .filter(fv => fv.vaultId === vaultId);

  // Apply filters
  if (category) {
    allFileVersions = allFileVersions.filter(fv => 
      fv.category.toLowerCase().includes(category.toLowerCase())
    );
  }
  
  if (state) {
    allFileVersions = allFileVersions.filter(fv => 
      fv.state.toLowerCase().includes(state.toLowerCase())
    );
  }
  
  if (isCheckedOut !== undefined) {
    const checkedOutFilter = isCheckedOut === 'true';
    allFileVersions = allFileVersions.filter(fv => fv.isCheckedOut === checkedOutFilter);
  }
  
  if (filter) {
    allFileVersions = allFileVersions.filter(fv => 
      fv.name.toLowerCase().includes(filter.toLowerCase()) ||
      fv.category.toLowerCase().includes(filter.toLowerCase()) ||
      fv.state.toLowerCase().includes(filter.toLowerCase())
    );
  }

  // Apply sorting
  allFileVersions.sort((a, b) => {
    let aVal = a[orderBy];
    let bVal = b[orderBy];
    
    if (orderBy === 'lastModifiedDate' || orderBy === 'createDate') {
      aVal = new Date(aVal);
      bVal = new Date(bVal);
    }
    
    if (orderDirection === 'desc') {
      return bVal > aVal ? 1 : -1;
    } else {
      return aVal > bVal ? 1 : -1;
    }
  });

  const limitNum = Math.min(parseInt(limit), 100);
  let startIndex = 0;
  
  // Handle cursor-based pagination
  if (cursorState) {
    try {
      const decoded = Buffer.from(cursorState, 'base64').toString();
      const cursorData = JSON.parse(decoded);
      startIndex = cursorData.startIndex || 0;
    } catch (e) {
      // Invalid cursor, start from beginning
    }
  }

  const results = allFileVersions.slice(startIndex, startIndex + limitNum);
  const hasMore = startIndex + limitNum < allFileVersions.length;
  
  let nextUrl = null;
  if (hasMore) {
    const nextCursor = Buffer.from(JSON.stringify({ 
      startIndex: startIndex + limitNum,
      filters: { category, state, isCheckedOut, filter, orderBy, orderDirection }
    })).toString('base64');
    
    const params = new URLSearchParams({ limit: limit.toString(), cursorState: nextCursor });
    if (category) params.append('category', category);
    if (state) params.append('state', state);
    if (isCheckedOut) params.append('isCheckedOut', isCheckedOut);
    if (filter) params.append('filter', filter);
    if (orderBy !== 'lastModifiedDate') params.append('orderBy', orderBy);
    if (orderDirection !== 'desc') params.append('orderDirection', orderDirection);
    
    nextUrl = `/AutodeskDM/Services/api/vault/v2/vaults/${vaultId}/file-versions?${params.toString()}`;
  }

  // Include referenced entities
  const included = {
    folder: {},
    file: {}
  };
  
  results.forEach(fileVersion => {
    // Include parent folder
    if (fileVersion.parentFolderId && req.vaultData.folders.has(fileVersion.parentFolderId)) {
      const folder = req.vaultData.folders.get(fileVersion.parentFolderId);
      included.folder[fileVersion.parentFolderId] = folder;
    }
    
    // Include file reference
    if (fileVersion.file && req.vaultData.files.has(fileVersion.file.id)) {
      const file = req.vaultData.files.get(fileVersion.file.id);
      included.file[fileVersion.file.id] = file;
    }
  });

  res.json({
    pagination: {
      limit: limitNum,
      totalResults: allFileVersions.length,
      nextUrl,
      indexingStatus: "IndexingComplete"
    },
    results,
    included
  });
});

// Get specific file version
router.get('/vaults/:vaultId/file-versions/:fileVersionId', (req, res) => {
  const { vaultId, fileVersionId } = req.params;
  
  const fileVersion = req.vaultData.fileVersions.get(fileVersionId);
  if (!fileVersion || fileVersion.vaultId !== vaultId) {
    return res.status(404).json({ error: 'File version not found' });
  }

  // Include related entities
  const included = {
    folder: {},
    file: {}
  };
  
  if (fileVersion.parentFolderId && req.vaultData.folders.has(fileVersion.parentFolderId)) {
    const folder = req.vaultData.folders.get(fileVersion.parentFolderId);
    included.folder[fileVersion.parentFolderId] = folder;
  }
  
  if (fileVersion.file && req.vaultData.files.has(fileVersion.file.id)) {
    const file = req.vaultData.files.get(fileVersion.file.id);
    included.file[fileVersion.file.id] = file;
  }

  res.json({
    ...fileVersion,
    included
  });
});

// Get files
router.get('/vaults/:vaultId/files', (req, res) => {
  const { vaultId } = req.params;
  const { limit = 10, filter, category } = req.query;
  
  let files = Array.from(req.vaultData.files.values())
    .filter(file => file.vaultId === vaultId);
    
  if (filter) {
    files = files.filter(file => 
      file.name.toLowerCase().includes(filter.toLowerCase())
    );
  }

  files = files.slice(0, parseInt(limit));

  res.json({
    pagination: {
      limit: parseInt(limit),
      totalResults: files.length,
      indexingStatus: "IndexingComplete"
    },
    results: files
  });
});

// Get specific file
router.get('/vaults/:vaultId/files/:fileId', (req, res) => {
  const { vaultId, fileId } = req.params;
  
  const file = req.vaultData.files.get(fileId);
  if (!file || file.vaultId !== vaultId) {
    return res.status(404).json({ error: 'File not found' });
  }

  res.json(file);
});

// Get folders
router.get('/vaults/:vaultId/folders', (req, res) => {
  const { vaultId } = req.params;
  const { limit = 10, filter, parentId } = req.query;
  
  let folders = Array.from(req.vaultData.folders.values())
    .filter(folder => folder.vaultId === vaultId);
    
  if (parentId) {
    folders = folders.filter(folder => folder.parentId === parentId);
  }
    
  if (filter) {
    folders = folders.filter(folder => 
      folder.name.toLowerCase().includes(filter.toLowerCase()) ||
      folder.fullName.toLowerCase().includes(filter.toLowerCase())
    );
  }

  folders = folders.slice(0, parseInt(limit));

  res.json({
    pagination: {
      limit: parseInt(limit),
      totalResults: folders.length,
      indexingStatus: "IndexingComplete"
    },
    results: folders
  });
});

// Get specific folder
router.get('/vaults/:vaultId/folders/:folderId', (req, res) => {
  const { vaultId, folderId } = req.params;
  
  const folder = req.vaultData.folders.get(folderId);
  if (!folder || folder.vaultId !== vaultId) {
    return res.status(404).json({ error: 'Folder not found' });
  }

  res.json(folder);
});

export default router;
