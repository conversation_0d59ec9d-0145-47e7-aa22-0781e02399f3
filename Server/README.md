# Vault MCP Server

A Model Context Protocol (MCP) server with Autodesk Vault API integration and time utilities.

## Features

- **Vault API Integration**: Complete integration with Autodesk Vault REST API
- **Time Tools**: Built-in tools for getting current time in various formats
- **Extensible Architecture**: Easy to add new tools without modifying core server code
- **Environment Configuration**: Configurable API endpoints and authentication
- **Standard MCP Protocol**: Compatible with any MCP client

## Built-in Tools

### Time Tools

#### get_current_time
Get the current date and time in various formats.

**Parameters:**
- `format` (optional): "iso", "local", "utc", or "timestamp" (default: "iso")
- `timezone` (optional): Timezone string (e.g., "America/New_York", "Europe/London")

#### get_time_info
Get detailed time information including timezone, day of week, etc.

**Parameters:**
- `timezone` (optional): Timezone string

### Vault Tools

#### get_vault_file_versions
Get file versions from Autodesk Vault with optional filtering and pagination.

**Parameters:**
- `vaultId` (optional): Vault ID to query (default: from config)
- `limit` (optional): Number of results (1-100, default: 10)
- `filter` (optional): Text filter for names, categories, or states
- `category` (optional): Filter by category (e.g., "Base", "Design")
- `state` (optional): Filter by state (e.g., "Released", "Work in Progress")
- `isCheckedOut` (optional): Filter by checkout status
- `orderBy` (optional): Sort field (default: "lastModifiedDate")
- `orderDirection` (optional): "asc" or "desc" (default: "desc")

#### get_vault_file_version
Get a specific file version by ID.

**Parameters:**
- `vaultId` (optional): Vault ID
- `fileVersionId`: File version ID to retrieve

#### get_vault_files
Get files from Autodesk Vault with optional filtering.

**Parameters:**
- `vaultId` (optional): Vault ID to query
- `limit` (optional): Number of results (1-100, default: 10)
- `filter` (optional): Text filter for file names

#### get_vault_folders
Get folders from Autodesk Vault with optional filtering.

**Parameters:**
- `vaultId` (optional): Vault ID to query
- `limit` (optional): Number of results (1-100, default: 10)
- `filter` (optional): Text filter for folder names
- `parentId` (optional): Parent folder ID to filter by

#### get_vault_info
Get information about available Autodesk Vaults.

**Parameters:**
- `vaultId` (optional): Specific vault ID, or leave empty for all vaults

#### search_vault_files
Search for files in Autodesk Vault with advanced filtering.

**Parameters:**
- `query`: Search query for file names, categories, or states
- `vaultId` (optional): Vault ID to search in
- `limit` (optional): Maximum results (1-50, default: 20)
- `fileType` (optional): Filter by file extension (e.g., "ipt", "iam", "dwg")
- `checkedOutOnly` (optional): Only show checked out files
- `recentOnly` (optional): Only show recently modified files

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Edit `.env` with your Vault API settings:

```env
# Vault API Configuration
VAULT_API_URL=http://localhost:3000/AutodeskDM/Services/api/vault/v2
VAULT_AUTH_TOKEN=AuIPTf4KYLTYGVnOHQ0cuolwCW2a...
VAULT_ID=117
VAULT_API_TIMEOUT=30000
```

### For Development/Testing
- Use the mock Vault API server (see VaultMockAPI folder)
- Default configuration works with the mock server

### For Production
- Update `VAULT_API_URL` to your actual Vault server
- Update `VAULT_AUTH_TOKEN` with your real Bearer token
- Update `VAULT_ID` with your target vault ID

## Running the Server

```bash
npm start
```

Or directly:
```bash
node index.js
```

## Adding New Tools

To add a new tool, create a file in the `tools/` directory following this pattern:

```javascript
import { z } from "zod";

export const myTool = {
  name: "my_tool",
  description: "Description of what the tool does",
  inputSchema: z.object({
    param1: z.string().describe("Description of parameter")
  }),
  handler: async ({ param1 }) => {
    // Tool logic here
    return {
      content: [
        {
          type: "text",
          text: `Result: ${param1}`
        }
      ]
    };
  }
};
```

Then import and register it in the server's `setupDefaultTools()` method.

## Architecture

The server uses a modular architecture where:
- Core server logic is in `index.js`
- Individual tools are in the `tools/` directory
- Tools are registered dynamically using the `addTool()` method
- Each tool defines its own schema and handler function
