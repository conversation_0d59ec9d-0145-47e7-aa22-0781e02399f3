{"name": "mcp-server", "version": "1.0.0", "type": "module", "main": "index.js", "bin": {"mcp-server": "./index.js"}, "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "model-context-protocol", "server"], "author": "", "license": "ISC", "description": "MCP Server with extensible tools", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "axios": "^1.10.0"}}