# Vault API Configuration
# Copy this file to .env and update with your actual Vault API settings

# Vault API Base URL
# For development/testing with mock API:
VAULT_API_URL=http://localhost:3000/AutodeskDM/Services/api/vault/v2

# For production with real Vault server:
# VAULT_API_URL=https://your-vault-server.com/AutodeskDM/Services/api/vault/v2

# Vault Authentication Token
# For development/testing:
VAULT_AUTH_TOKEN=AuIPTf4KYLTYGVnOHQ0cuolwCW2a...

# For production, use your actual Bearer token:
# VAULT_AUTH_TOKEN=your_actual_bearer_token_here

# Default Vault ID to use when not specified
VAULT_ID=117

# API Timeout (milliseconds)
VAULT_API_TIMEOUT=30000
