using McpClient.Services;
using System.Text.Json;

namespace McpClient.Examples;

public static class ToolCallExample
{
    public static void DemonstrateToolExtraction()
    {
        var mcpService = new McpClientService(null!, null!, null!);
        
        // Example 1: Search query
        var question1 = "search for CAD files checked out by john";
        var toolCall1 = mcpService.ExtractToolCallFromQuestion(question1);
        Console.WriteLine("Question: " + question1);
        Console.WriteLine("Tool Call: " + JsonSerializer.Serialize(toolCall1, new JsonSerializerOptions { WriteIndented = true }));
        Console.WriteLine();
        
        // Example 2: Recent files
        var question2 = "show me recent inventor drawings";
        var toolCall2 = mcpService.ExtractToolCallFromQuestion(question2);
        Console.WriteLine("Question: " + question2);
        Console.WriteLine("Tool Call: " + JsonSerializer.Serialize(toolCall2, new JsonSerializerOptions { WriteIndented = true }));
        Console.WriteLine();
        
        // Example 3: Statistics
        var question3 = "get detailed vault statistics";
        var toolCall3 = mcpService.ExtractToolCallFromQuestion(question3);
        Console.WriteLine("Question: " + question3);
        Console.WriteLine("Tool Call: " + JsonSerializer.Serialize(toolCall3, new JsonSerializerOptions { WriteIndented = true }));
        Console.WriteLine();
        
        // Example 4: User activity
        var question4 = "who has been working on files lately";
        var toolCall4 = mcpService.ExtractToolCallFromQuestion(question4);
        Console.WriteLine("Question: " + question4);
        Console.WriteLine("Tool Call: " + JsonSerializer.Serialize(toolCall4, new JsonSerializerOptions { WriteIndented = true }));
        Console.WriteLine();
        
        // Example 5: Date range
        var question5 = "find files modified since last week";
        var toolCall5 = mcpService.ExtractToolCallFromQuestion(question5);
        Console.WriteLine("Question: " + question5);
        Console.WriteLine("Tool Call: " + JsonSerializer.Serialize(toolCall5, new JsonSerializerOptions { WriteIndented = true }));
    }
}

/*
Expected Output:

Question: search for CAD files checked out by john
Tool Call: {
  "tool": "search_vault_files",
  "parameters": {
    "query": "CAD files",
    "fileType": "ipt",
    "checkedOutOnly": true,
    "recentOnly": null,
    "userName": "john",
    "limit": 20
  }
}

Question: show me recent inventor drawings
Tool Call: {
  "tool": "search_vault_files",
  "parameters": {
    "query": "inventor drawings",
    "fileType": "ipt",
    "checkedOutOnly": null,
    "recentOnly": true,
    "userName": null,
    "limit": 20
  }
}

Question: get detailed vault statistics
Tool Call: {
  "tool": "get_vault_file_stats",
  "parameters": {
    "includeDetails": true
  }
}

Question: who has been working on files lately
Tool Call: {
  "tool": "get_vault_user_activity",
  "parameters": {
    "userName": null,
    "limit": 20
  }
}

Question: find files modified since last week
Tool Call: {
  "tool": "get_vault_files_by_date_range",
  "parameters": {
    "startDate": "2024-06-17T00:00:00Z",
    "endDate": null,
    "limit": 20
  }
}
*/
