using McpClient.Services;
using System.Text.Json;

namespace McpClient.Examples;

public static class SimplifiedArchitectureExample
{
    public static async Task DemonstrateSimplifiedArchitecture()
    {
        Console.WriteLine("=== Simplified MCP Architecture Demo ===\n");
        
        // This demonstrates the proper MCP architecture:
        // User → Client → AI (tool selection) → MCP Server → API → Response → AI (formatting) → User
        
        Console.WriteLine("Architecture Flow:");
        Console.WriteLine("1. 🧑 User asks question");
        Console.WriteLine("2. 🤖 Client fetches available tools from MCP server (/tools endpoint)");
        Console.WriteLine("3. 🧠 AI selects best tool and extracts parameters");
        Console.WriteLine("4. 📡 Client sends tool request to MCP server (/execute endpoint)");
        Console.WriteLine("5. 🔧 MCP server executes tool (makes real API calls)");
        Console.WriteLine("6. 📊 MCP server returns results");
        Console.WriteLine("7. 🧠 AI formats final response for user");
        Console.WriteLine("8. 💬 User gets natural language answer\n");
        
        // Example 1: Demonstrate tool discovery
        Console.WriteLine("=== Step 1: Tool Discovery ===");
        await DemonstrateToolDiscovery();
        Console.WriteLine();
        
        // Example 2: Demonstrate tool execution
        Console.WriteLine("=== Step 2: Tool Execution ===");
        await DemonstrateToolExecution();
        Console.WriteLine();
        
        // Example 3: Show the complete flow
        Console.WriteLine("=== Step 3: Complete Flow Example ===");
        await DemonstrateCompleteFlow();
    }
    
    private static async Task DemonstrateToolDiscovery()
    {
        Console.WriteLine("Fetching tools from MCP server...");
        
        using var httpClient = new HttpClient();
        try
        {
            var response = await httpClient.GetAsync("http://localhost:3001/tools");
            var content = await response.Content.ReadAsStringAsync();
            var toolsResponse = JsonSerializer.Deserialize<ToolsResponse>(content);
            
            Console.WriteLine($"Found {toolsResponse?.Tools?.Count ?? 0} available tools:");
            
            if (toolsResponse?.Tools != null)
            {
                foreach (var tool in toolsResponse.Tools.Take(5)) // Show first 5
                {
                    Console.WriteLine($"  • {tool.Name}: {tool.Description}");
                    if (tool.Parameters?.Any() == true)
                    {
                        var requiredParams = tool.Parameters.Where(p => p.Value.Required).Select(p => p.Key);
                        var optionalParams = tool.Parameters.Where(p => !p.Value.Required).Select(p => p.Key);
                        
                        if (requiredParams.Any())
                            Console.WriteLine($"    Required: {string.Join(", ", requiredParams)}");
                        if (optionalParams.Any())
                            Console.WriteLine($"    Optional: {string.Join(", ", optionalParams)}");
                    }
                }
                
                if (toolsResponse.Tools.Count > 5)
                    Console.WriteLine($"  ... and {toolsResponse.Tools.Count - 5} more tools");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
    
    private static async Task DemonstrateToolExecution()
    {
        Console.WriteLine("Executing a tool directly...");
        
        var toolRequest = new
        {
            tool = "calculate_math",
            @params = new
            {
                expression = "2 + 3 * 4",
                angleUnit = "degrees"
            }
        };
        
        using var httpClient = new HttpClient();
        try
        {
            var json = JsonSerializer.Serialize(toolRequest);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
            
            Console.WriteLine($"Sending: {json}");
            
            var response = await httpClient.PostAsync("http://localhost:3001/execute", content);
            var result = await response.Content.ReadAsStringAsync();
            
            Console.WriteLine($"Result: {result}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
    
    private static async Task DemonstrateCompleteFlow()
    {
        Console.WriteLine("Simulating complete user interaction flow...");
        
        var userQuestions = new[]
        {
            "What time is it?",
            "Calculate 15 * 8 + 12",
            "Search for CAD files in the vault",
            "Count the words in 'Hello world this is a test'"
        };
        
        foreach (var question in userQuestions)
        {
            Console.WriteLine($"\n👤 User: \"{question}\"");
            Console.WriteLine("🤖 Client: Analyzing question and selecting appropriate tool...");
            
            // This would normally use AI, but for demo we'll simulate
            var (toolName, parameters) = SimulateAiToolSelection(question);
            
            Console.WriteLine($"🧠 AI Selected: {toolName} with params: {JsonSerializer.Serialize(parameters)}");
            Console.WriteLine($"📡 Client: Executing tool on MCP server...");
            
            // Execute the tool
            var result = await ExecuteToolForDemo(toolName, parameters);
            Console.WriteLine($"📊 MCP Result: {result}");
            Console.WriteLine($"🧠 AI: Formatting response for user...");
            Console.WriteLine($"💬 Final Answer: {SimulateAiResponseFormatting(question, result)}");
        }
    }
    
    private static (string toolName, Dictionary<string, object> parameters) SimulateAiToolSelection(string question)
    {
        var lowerQuestion = question.ToLower();
        
        if (lowerQuestion.Contains("time"))
            return ("get_current_time", new Dictionary<string, object> { ["format"] = "local" });
        
        if (lowerQuestion.Contains("calculate") || lowerQuestion.Contains("*") || lowerQuestion.Contains("+"))
        {
            var expression = ExtractMathExpression(question);
            return ("calculate_math", new Dictionary<string, object> { ["expression"] = expression });
        }
        
        if (lowerQuestion.Contains("search") && lowerQuestion.Contains("vault"))
            return ("search_vault_files", new Dictionary<string, object> { ["query"] = "CAD files" });
        
        if (lowerQuestion.Contains("count") && lowerQuestion.Contains("word"))
        {
            var text = ExtractTextFromQuestion(question);
            return ("process_text", new Dictionary<string, object> { ["text"] = text, ["operation"] = "count_words" });
        }
        
        return ("get_current_time", new Dictionary<string, object>());
    }
    
    private static string ExtractMathExpression(string question)
    {
        // Simple extraction - in real implementation, AI would do this
        var parts = question.Split(' ');
        for (int i = 0; i < parts.Length - 2; i++)
        {
            if (int.TryParse(parts[i], out _) && parts[i + 1] == "*" && int.TryParse(parts[i + 2], out _))
            {
                return string.Join(" ", parts.Skip(i).Take(5)); // Take a reasonable portion
            }
        }
        return question;
    }
    
    private static string ExtractTextFromQuestion(string question)
    {
        var start = question.IndexOf('\'');
        var end = question.LastIndexOf('\'');
        if (start >= 0 && end > start)
            return question.Substring(start + 1, end - start - 1);
        return "sample text";
    }
    
    private static async Task<string> ExecuteToolForDemo(string toolName, Dictionary<string, object> parameters)
    {
        using var httpClient = new HttpClient();
        try
        {
            var request = new { tool = toolName, @params = parameters };
            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
            
            var response = await httpClient.PostAsync("http://localhost:3001/execute", content);
            return await response.Content.ReadAsStringAsync();
        }
        catch (Exception ex)
        {
            return $"Error: {ex.Message}";
        }
    }
    
    private static string SimulateAiResponseFormatting(string question, string toolResult)
    {
        // In real implementation, AI would format this naturally
        if (question.ToLower().Contains("time"))
            return "The current time is shown above.";
        if (question.ToLower().Contains("calculate"))
            return "Here's the calculation result.";
        if (question.ToLower().Contains("search"))
            return "I found the following files in the vault.";
        if (question.ToLower().Contains("count"))
            return "The word count is shown above.";
        
        return "Here's the information you requested.";
    }
}
