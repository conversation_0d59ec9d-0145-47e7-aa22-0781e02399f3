using System.Text.Json;
using System.Text;

namespace McpClient.Services;

public class SimplifiedMcpClientService
{
    private readonly HttpClient _httpClient;
    private readonly IAiService _aiService;
    private readonly string _mcpServerUrl;
    private List<ToolInfo>? _availableTools;

    public SimplifiedMcpClientService(IAiService aiService, string mcpServerUrl = "http://localhost:3001")
    {
        _httpClient = new HttpClient();
        _aiService = aiService;
        _mcpServerUrl = mcpServerUrl;
    }

    public async Task<string> ProcessUserQuestionAsync(string userQuestion)
    {
        try
        {
            // Step 1: Fetch available tools from MCP server
            var tools = await GetAvailableToolsAsync();
            
            // Step 2: Use AI to determine best tool and parameters
            var toolSelection = await SelectToolWithAiAsync(userQuestion, tools);
            
            // Step 3: Execute the selected tool on MCP server
            var toolResult = await ExecuteToolAsync(toolSelection.Tool, toolSelection.Params);
            
            // Step 4: Use AI to format the final response for the user
            var finalResponse = await FormatResponseWithAiAsync(userQuestion, toolResult);
            
            return finalResponse;
        }
        catch (Exception ex)
        {
            return $"Error processing question: {ex.Message}";
        }
    }

    private async Task<List<ToolInfo>> GetAvailableToolsAsync()
    {
        if (_availableTools != null)
            return _availableTools;

        try
        {
            var response = await _httpClient.GetAsync($"{_mcpServerUrl}/tools");
            response.EnsureSuccessStatusCode();
            
            var content = await response.Content.ReadAsStringAsync();
            var toolsResponse = JsonSerializer.Deserialize<ToolsResponse>(content);
            
            _availableTools = toolsResponse?.Tools ?? new List<ToolInfo>();
            return _availableTools;
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to fetch tools from MCP server: {ex.Message}");
        }
    }

    private async Task<ToolSelection> SelectToolWithAiAsync(string userQuestion, List<ToolInfo> tools)
    {
        var toolsDescription = string.Join("\n", tools.Select(t => 
            $"- {t.Name}: {t.Description}" + 
            (t.Parameters?.Any() == true ? $" (params: {string.Join(", ", t.Parameters.Keys)})" : "")));

        var prompt = $@"
User Question: {userQuestion}

Available Tools:
{toolsDescription}

Based on the user's question, select the most appropriate tool and extract any necessary parameters.

Respond with JSON in this exact format:
{{
  ""tool"": ""tool_name"",
  ""params"": {{
    ""param1"": ""value1"",
    ""param2"": ""value2""
  }}
}}

If no parameters are needed, use an empty object for params: {{}}
";

        var aiResponse = await _aiService.ProcessQuestionAsync(prompt);
        
        try
        {
            // Extract JSON from AI response
            var jsonStart = aiResponse.IndexOf('{');
            var jsonEnd = aiResponse.LastIndexOf('}') + 1;
            
            if (jsonStart >= 0 && jsonEnd > jsonStart)
            {
                var jsonStr = aiResponse.Substring(jsonStart, jsonEnd - jsonStart);
                var selection = JsonSerializer.Deserialize<ToolSelection>(jsonStr);
                return selection ?? new ToolSelection { Tool = "get_current_time", Params = new Dictionary<string, object>() };
            }
        }
        catch (JsonException)
        {
            // Fallback if AI response is not valid JSON
        }

        // Default fallback
        return new ToolSelection 
        { 
            Tool = "get_current_time", 
            Params = new Dictionary<string, object>() 
        };
    }

    private async Task<string> ExecuteToolAsync(string toolName, Dictionary<string, object> parameters)
    {
        try
        {
            var request = new
            {
                tool = toolName,
                @params = parameters
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync($"{_mcpServerUrl}/execute", content);
            response.EnsureSuccessStatusCode();
            
            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<ToolExecutionResponse>(responseContent);
            
            return result?.Result?.ToString() ?? "No result returned";
        }
        catch (Exception ex)
        {
            return $"Error executing tool {toolName}: {ex.Message}";
        }
    }

    private async Task<string> FormatResponseWithAiAsync(string originalQuestion, string toolResult)
    {
        var prompt = $@"
Original User Question: {originalQuestion}

Tool Result: {toolResult}

Please provide a natural, helpful response to the user based on the tool result. 
Make it conversational and directly answer their question.
";

        return await _aiService.ProcessQuestionAsync(prompt);
    }
}

// Data models
public class ToolsResponse
{
    public List<ToolInfo> Tools { get; set; } = new();
}

public class ToolInfo
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Dictionary<string, ParameterInfo>? Parameters { get; set; }
}

public class ParameterInfo
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool Required { get; set; }
    public object? Default { get; set; }
}

public class ToolSelection
{
    public string Tool { get; set; } = string.Empty;
    public Dictionary<string, object> Params { get; set; } = new();
}

public class ToolExecutionResponse
{
    public object? Result { get; set; }
    public string? Error { get; set; }
}
