using McpClient.Models;

namespace McpClient.Services;

public interface IMcpClientService
{
    Task<AskResponse> AskQuestionAsync(AskRequest request);
    Task<List<ServerInfo>> GetConnectedServersAsync();
    Task<bool> ConnectToServerAsync(string serverPath);
    Task<bool> DisconnectFromServerAsync(string serverPath);
    Task<List<string>> GetAvailableToolsAsync(string serverPath);
    object ExtractToolCallFromQuestion(string question);
}
