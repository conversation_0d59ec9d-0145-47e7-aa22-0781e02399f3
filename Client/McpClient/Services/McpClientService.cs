using McpClient.Models;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace McpClient.Services;

public class McpClientService : IMcpClientService, IDisposable
{
    private readonly ILogger<McpClientService> _logger;
    private readonly IAiService _aiService;
    private readonly ConcurrentDictionary<string, ServerConnection> _connections = new();
    private readonly string _defaultServerPath;

    public McpClientService(ILogger<McpClientService> logger, IAiService aiService, IConfiguration configuration)
    {
        _logger = logger;
        _aiService = aiService;
        _defaultServerPath = configuration["DefaultServerPath"] ?? "/Users/<USER>/mcp_vault/Server/index.js";
    }

    public async Task<AskResponse> AskQuestionAsync(AskRequest request)
    {
        try
        {
            var serverPath = request.ServerPath ?? _defaultServerPath;
            var connection = await GetOrCreateConnectionAsync(serverPath);

            if (connection?.IsConnected != true)
            {
                return new AskResponse
                {
                    Success = false,
                    Error = "Failed to connect to MCP server",
                    Answer = "Unable to process your question due to server connection issues."
                };
            }

            var toolsUsed = new List<string>();
            var answer = await ProcessQuestionWithToolsAsync(connection, request.Question, toolsUsed);

            return new AskResponse
            {
                Success = true,
                Answer = answer,
                ToolsUsed = toolsUsed,
                Metadata = new Dictionary<string, object>
                {
                    ["serverPath"] = serverPath,
                    ["timestamp"] = DateTime.UtcNow,
                    ["availableTools"] = connection.AvailableTools
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing question: {Question}", request.Question);
            return new AskResponse
            {
                Success = false,
                Error = ex.Message,
                Answer = "An error occurred while processing your question."
            };
        }
    }

    private async Task<string> ProcessQuestionWithToolsAsync(ServerConnection connection, string question, List<string> toolsUsed)
    {
        try
        {
            // Analyze the question to determine which tools are needed
            var toolPlan = AnalyzeQuestionForTools(question, connection.AvailableTools);

            if (toolPlan.Count == 0)
            {
                return GenerateBasicResponse(question);
            }

            // Execute tools in sequence, building context
            var toolResults = new Dictionary<string, string>();
            var combinedContext = new List<string>();

            foreach (var toolCall in toolPlan)
            {
                try
                {
                    var result = await ExecuteToolAsync(connection, toolCall, toolResults);
                    toolResults[toolCall.ToolName] = result;
                    toolsUsed.Add(toolCall.ToolName);
                    combinedContext.Add($"{toolCall.ToolName}: {result}");

                    _logger.LogInformation("Successfully executed tool {ToolName}, result length: {Length}",
                        toolCall.ToolName, result?.Length ?? 0);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to execute tool {ToolName}", toolCall.ToolName);
                    combinedContext.Add($"{toolCall.ToolName}: Failed to execute");
                }
            }

            // Use AI service to generate intelligent response with all tool results
            var allToolResults = string.Join("\n\n", combinedContext);
            var aiResponse = await _aiService.GenerateResponseAsync(question, allToolResults, connection.AvailableTools);
            return aiResponse;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process question with tools");
            return GenerateBasicResponse(question);
        }
    }

    private static Task<string> CallServerToolAsync(string serverPath, string toolName, object parameters)
    {
        // For now, return a simulated time response
        // In a full implementation, this would communicate with the actual MCP server
        if (toolName == "get_current_time")
        {
            return Task.FromResult($"Current time (local): {DateTime.Now:F}");
        }

        return Task.FromResult("Tool result not available");
    }

    private static bool ContainsTimeKeywords(string question)
    {
        var timeKeywords = new[] {
            "time", "date", "when", "now", "current", "today", "clock", "hour", "minute",
            "which date", "what date", "what time", "which time", "day", "month", "year",
            "morning", "afternoon", "evening", "night", "timezone", "calendar"
        };
        return timeKeywords.Any(keyword => question.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private static bool ContainsVaultKeywords(string question)
    {
        var vaultKeywords = new[] {
            "vault", "file", "files", "folder", "folders", "document", "documents",
            "version", "versions", "drawing", "drawings", "assembly", "assemblies",
            "part", "parts", "cad", "dwg", "ipt", "iam", "idw", "pdf",
            "search", "find", "lookup", "browse", "list", "show me",
            "checked out", "checkout", "checkin", "latest", "recent",
            "engineering", "design", "manufacturing", "project"
        };
        return vaultKeywords.Any(keyword => question.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private List<ToolCall> AnalyzeQuestionForTools(string question, List<string> availableTools)
    {
        var toolPlan = new List<ToolCall>();
        var questionLower = question.ToLowerInvariant();

        // Check for time-dependent queries that need current time first
        if (RequiresTimeContext(questionLower))
        {
            if (availableTools.Contains("get_current_time"))
            {
                toolPlan.Add(new ToolCall
                {
                    ToolName = "get_current_time",
                    Purpose = "Get current time for date calculations",
                    Parameters = new { format = "iso" }
                });
            }
        }

        // Check for Vault operations
        if (ContainsVaultKeywords(question))
        {
            var vaultTool = DetermineVaultTool(questionLower, availableTools);
            if (vaultTool != null)
            {
                toolPlan.Add(vaultTool);
            }
        }
        // Check for time-only queries
        else if (ContainsTimeKeywords(question) && availableTools.Contains("get_current_time"))
        {
            toolPlan.Add(new ToolCall
            {
                ToolName = "get_current_time",
                Purpose = "Get current time",
                Parameters = new { format = "local" }
            });
        }

        _logger.LogInformation("Tool plan for question '{Question}': {Tools}",
            question, string.Join(", ", toolPlan.Select(t => t.ToolName)));

        return toolPlan;
    }

    private static bool RequiresTimeContext(string questionLower)
    {
        var timeContextKeywords = new[]
        {
            "last", "recent", "today", "yesterday", "this week", "last week",
            "this month", "last month", "days ago", "hours ago", "minutes ago",
            "modified in", "created in", "updated in", "changed in",
            "since", "before", "after", "between"
        };

        return timeContextKeywords.Any(keyword => questionLower.Contains(keyword));
    }

    private ToolCall? DetermineVaultTool(string questionLower, List<string> availableTools)
    {
        // Statistics and analytics queries
        if ((questionLower.Contains("statistics") || questionLower.Contains("stats") ||
             questionLower.Contains("how many") || questionLower.Contains("count")) &&
            availableTools.Contains("get_vault_file_stats"))
        {
            return new ToolCall
            {
                ToolName = "get_vault_file_stats",
                Purpose = "Get vault statistics",
                Parameters = new { includeDetails = questionLower.Contains("detail") }
            };
        }

        // User activity queries
        if ((questionLower.Contains("user") || questionLower.Contains("who") ||
             questionLower.Contains("checked out") || questionLower.Contains("activity")) &&
            availableTools.Contains("get_vault_user_activity"))
        {
            return new ToolCall
            {
                ToolName = "get_vault_user_activity",
                Purpose = "Get user activity information",
                Parameters = new { limit = 20 }
            };
        }

        // Date range queries (requires time context)
        if (RequiresTimeContext(questionLower) &&
            availableTools.Contains("get_vault_files_by_date_range"))
        {
            return new ToolCall
            {
                ToolName = "get_vault_files_by_date_range",
                Purpose = "Get files modified in date range",
                Parameters = new {
                    startDate = CalculateStartDate(questionLower),
                    limit = 30
                }
            };
        }

        // Advanced search queries
        if ((questionLower.Contains("search") || questionLower.Contains("find")) &&
            availableTools.Contains("search_vault_files"))
        {
            return new ToolCall
            {
                ToolName = "search_vault_files",
                Purpose = "Search for files in Vault",
                Parameters = new {
                    query = ExtractSearchQuery(questionLower),
                    recentOnly = questionLower.Contains("recent"),
                    checkedOutOnly = questionLower.Contains("checked out")
                }
            };
        }

        // File version queries
        if ((questionLower.Contains("file") || questionLower.Contains("version") ||
             questionLower.Contains("modified") || questionLower.Contains("recent")) &&
            availableTools.Contains("get_vault_file_versions"))
        {
            return new ToolCall
            {
                ToolName = "get_vault_file_versions",
                Purpose = "Get file versions from Vault",
                Parameters = new { limit = 20, orderBy = "lastModifiedDate", orderDirection = "desc" }
            };
        }

        // Folder queries
        if (questionLower.Contains("folder") && availableTools.Contains("get_vault_folders"))
        {
            return new ToolCall
            {
                ToolName = "get_vault_folders",
                Purpose = "Get folders from Vault",
                Parameters = new { limit = 10 }
            };
        }

        // General vault info
        if (questionLower.Contains("vault") && availableTools.Contains("get_vault_info"))
        {
            return new ToolCall
            {
                ToolName = "get_vault_info",
                Purpose = "Get vault information",
                Parameters = new { }
            };
        }

        // Default to file versions for general file queries
        if (availableTools.Contains("get_vault_file_versions"))
        {
            return new ToolCall
            {
                ToolName = "get_vault_file_versions",
                Purpose = "Get file versions from Vault",
                Parameters = new { limit = 10 }
            };
        }

        return null;
    }

    private static string CalculateStartDate(string questionLower)
    {
        var now = DateTime.UtcNow;

        if (questionLower.Contains("last 7 days") || questionLower.Contains("last week"))
            return now.AddDays(-7).ToString("yyyy-MM-ddTHH:mm:ssZ");

        if (questionLower.Contains("last 30 days") || questionLower.Contains("last month"))
            return now.AddDays(-30).ToString("yyyy-MM-ddTHH:mm:ssZ");

        if (questionLower.Contains("today"))
            return now.Date.ToString("yyyy-MM-ddTHH:mm:ssZ");

        if (questionLower.Contains("yesterday"))
            return now.AddDays(-1).Date.ToString("yyyy-MM-ddTHH:mm:ssZ");

        // Default to last 7 days
        return now.AddDays(-7).ToString("yyyy-MM-ddTHH:mm:ssZ");
    }

    private static string ExtractSearchQuery(string questionLower)
    {
        // Extract search terms from the question
        var searchKeywords = new[] { "search for", "find", "look for", "show me" };

        foreach (var keyword in searchKeywords)
        {
            var index = questionLower.IndexOf(keyword);
            if (index >= 0)
            {
                var afterKeyword = questionLower.Substring(index + keyword.Length).Trim();
                var words = afterKeyword.Split(' ').Take(3); // Take first few words
                return string.Join(" ", words);
            }
        }

        return "files"; // Default search term
    }

    private async Task<string> ExecuteToolAsync(ServerConnection connection, ToolCall toolCall, Dictionary<string, string> previousResults)
    {
        return toolCall.ToolName switch
        {
            "get_current_time" => await CallServerToolAsync(connection.ServerPath, "get_current_time", toolCall.Parameters),

            // Basic Vault tools
            "get_vault_file_versions" or "search_vault_files" or "get_vault_folders" or "get_vault_info" =>
                await CallAdvancedVaultToolAsync(toolCall, previousResults),

            // Advanced Vault tools
            "get_vault_files_by_date_range" or "get_vault_file_stats" or "get_vault_user_activity" =>
                await CallAdvancedVaultToolAsync(toolCall, previousResults),

            _ => $"Tool {toolCall.ToolName} executed successfully"
        };
    }

    private static async Task<string> CallAdvancedVaultToolAsync(ToolCall toolCall, Dictionary<string, string> previousResults)
    {
        // Enhanced vault tool simulation with context awareness
        var hasTimeContext = previousResults.ContainsKey("get_current_time");

        return toolCall.ToolName switch
        {
            "search_vault_files" => await Task.FromResult(
                hasTimeContext
                    ? "Found 8 files matching your search criteria. Recent files include: Assembly2.iam (modified 2 days ago), Test1.ipt (modified 5 days ago), Drawing1.dwg (modified 1 week ago)"
                    : "Found 5 files matching your search criteria: Assembly2.iam, Test1.ipt, Drawing1.dwg, Part1.ipt, Assembly1.iam"),

            "get_vault_file_versions" => await Task.FromResult(
                hasTimeContext
                    ? "Retrieved 15 file versions. Files modified in the last 7 days: Assembly2.iam (v3, 2 days ago), Test1.ipt (v2, 5 days ago), NewPart.ipt (v1, 6 days ago)"
                    : "Retrieved 10 file versions from Vault. Latest files include Assembly2.iam (v3, checked out), Test1.ipt (v2, checked in)"),

            "get_vault_folders" => await Task.FromResult(
                "Found 5 folders: ColinTest (15 files), Engineering (23 files), Manufacturing (8 files), Archive (45 files), Templates (12 files)"),

            "get_vault_info" => await Task.FromResult(
                "Connected to Vault 117 (TestVault). Total: 52 files, 11 folders, 3 active users. Last backup: 2 days ago"),

            // Advanced tools
            "get_vault_files_by_date_range" => await Task.FromResult(
                hasTimeContext
                    ? "Found 12 files modified in the specified date range: Assembly2.iam (2 days ago), Test1.ipt (5 days ago), NewPart.ipt (6 days ago), Drawing1.dwg (1 week ago), and 8 others"
                    : "Found 8 files in the date range: Assembly2.iam, Test1.ipt, NewPart.ipt, Drawing1.dwg, Part2.ipt, Assembly3.iam, Sketch1.dwg, Manual.pdf"),

            "get_vault_file_stats" => await Task.FromResult(
                "Vault Statistics: 52 total files, 8 checked out, 245.7 MB total size, 12 files modified in last 7 days. File types: .ipt (23), .iam (15), .dwg (8), .pdf (4), .xlsx (2)"),

            "get_vault_user_activity" => await Task.FromResult(
                "User Activity: TestAccount (5 checked out, 12 recent files), Engineer1 (2 checked out, 8 recent files), Designer2 (1 checked out, 5 recent files)"),

            _ => await Task.FromResult($"Called {toolCall.ToolName} successfully")
        };
    }

    private async Task<string> CallVaultToolAsync(ServerConnection connection, string question, List<string> toolsUsed)
    {
        // Determine which Vault tool to use based on the question
        if (question.Contains("search", StringComparison.OrdinalIgnoreCase) &&
            connection.AvailableTools.Contains("search_vault_files"))
        {
            toolsUsed.Add("search_vault_files");
            return await SimulateVaultToolCall("search_vault_files", question);
        }

        if ((question.Contains("file", StringComparison.OrdinalIgnoreCase) ||
             question.Contains("version", StringComparison.OrdinalIgnoreCase)) &&
            connection.AvailableTools.Contains("get_vault_file_versions"))
        {
            toolsUsed.Add("get_vault_file_versions");
            return await SimulateVaultToolCall("get_vault_file_versions", question);
        }

        if (question.Contains("folder", StringComparison.OrdinalIgnoreCase) &&
            connection.AvailableTools.Contains("get_vault_folders"))
        {
            toolsUsed.Add("get_vault_folders");
            return await SimulateVaultToolCall("get_vault_folders", question);
        }

        if (question.Contains("vault", StringComparison.OrdinalIgnoreCase) &&
            connection.AvailableTools.Contains("get_vault_info"))
        {
            toolsUsed.Add("get_vault_info");
            return await SimulateVaultToolCall("get_vault_info", question);
        }

        // Default to file versions if no specific tool matches
        if (connection.AvailableTools.Contains("get_vault_file_versions"))
        {
            toolsUsed.Add("get_vault_file_versions");
            return await SimulateVaultToolCall("get_vault_file_versions", question);
        }

        return "Vault tools are available but could not determine the appropriate tool for your question.";
    }

    private async Task<List<string>> DiscoverToolsFromServerAsync(string serverPath)
    {
        try
        {
            // For this implementation, we'll use a known set of tools that the enhanced MCP server provides
            // In a full implementation, this would use the actual MCP protocol to discover tools dynamically

            var allKnownTools = new List<string>
            {
                // Time tools
                "get_current_time",
                "get_time_info",

                // Basic Vault tools
                "get_vault_file_versions",
                "get_vault_file_version",
                "get_vault_files",
                "get_vault_folders",
                "get_vault_info",
                "search_vault_files",

                // Advanced Vault tools
                "get_vault_files_by_date_range",
                "get_vault_file_stats",
                "get_vault_user_activity"
            };

            _logger.LogInformation("Using known tools from enhanced MCP server: {Tools}",
                string.Join(", ", allKnownTools));

            return await Task.FromResult(allKnownTools);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get tools, using minimal default tools");
            // Fallback to minimal tools if something goes wrong
            return ["get_current_time", "get_time_info"];
        }
    }



    private static Task<string> SimulateVaultToolCall(string toolName, string question)
    {
        // Simulate calling the Vault API
        // In a real implementation, this would call the actual MCP server
        return toolName switch
        {
            "search_vault_files" => Task.FromResult("Found 5 files matching your search criteria: Assembly2.iam, Test1.ipt, Drawing1.dwg, Part1.ipt, Assembly1.iam"),
            "get_vault_file_versions" => Task.FromResult("Retrieved 10 file versions from Vault. Latest files include Assembly2.iam (v3, checked out), Test1.ipt (v2, checked in)"),
            "get_vault_folders" => Task.FromResult("Found 3 folders: ColinTest, Engineering, Manufacturing"),
            "get_vault_info" => Task.FromResult("Connected to Vault 117 (TestVault) with 52 files and 11 folders"),
            _ => Task.FromResult($"Called {toolName} successfully")
        };
    }

    private static string GenerateTimeBasedResponse(string question, string timeInfo)
    {
        // Extract just the time/date from the MCP response
        var cleanTimeInfo = ExtractTimeFromResponse(timeInfo);

        if (question.Contains("what time", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("time is", StringComparison.OrdinalIgnoreCase))
            return $"The current time is {cleanTimeInfo}.";

        if (question.Contains("what date", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("which date", StringComparison.OrdinalIgnoreCase) ||
            question.Contains("today", StringComparison.OrdinalIgnoreCase))
            return $"Today is {cleanTimeInfo}.";

        if (question.Contains("when", StringComparison.OrdinalIgnoreCase))
            return $"It is currently {cleanTimeInfo}.";

        return $"The current date and time is {cleanTimeInfo}.";
    }

    private static string ExtractTimeFromResponse(string timeInfo)
    {
        // Extract the actual time from "Current time (local): Tuesday, June 24, 2025 4:35:32 PM"
        if (timeInfo.Contains("Current time (local):"))
        {
            var prefix = "Current time (local):";
            var timeStart = timeInfo.IndexOf(prefix) + prefix.Length;
            var extractedTime = timeInfo[timeStart..].Trim();

            // Remove any extra formatting characters
            extractedTime = extractedTime.Replace("\u202F", " "); // Remove narrow no-break space

            return extractedTime;
        }

        return timeInfo;
    }

    private static string GenerateBasicResponse(string question)
    {
        return $"I received your question: \"{question}\". " +
               "I'm a basic MCP client that can connect to MCP servers and use their tools. " +
               "Currently, I have access to time-related tools. " +
               "Try asking me about the current time or date!";
    }

    private async Task<ServerConnection?> GetOrCreateConnectionAsync(string serverPath)
    {
        if (_connections.TryGetValue(serverPath, out var existingConnection) &&
            existingConnection.IsConnected)
        {
            return existingConnection;
        }

        try
        {
            // Discover available tools from the MCP server
            var availableTools = await DiscoverToolsFromServerAsync(serverPath);

            var connection = new ServerConnection
            {
                ServerPath = serverPath,
                AvailableTools = availableTools,
                ConnectedAt = DateTime.UtcNow,
                IsConnected = true
            };

            _connections.AddOrUpdate(serverPath, connection, (key, old) => connection);

            _logger.LogInformation("Connected to MCP server at {ServerPath} with tools: {Tools}",
                serverPath, string.Join(", ", connection.AvailableTools));

            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to MCP server at {ServerPath}", serverPath);
            return null;
        }
    }



    public Task<List<ServerInfo>> GetConnectedServersAsync()
    {
        var servers = new List<ServerInfo>();

        foreach (var kvp in _connections)
        {
            servers.Add(new ServerInfo
            {
                Name = Path.GetFileNameWithoutExtension(kvp.Key),
                Path = kvp.Key,
                AvailableTools = kvp.Value.AvailableTools,
                IsConnected = kvp.Value.IsConnected,
                LastConnected = kvp.Value.ConnectedAt
            });
        }

        return Task.FromResult(servers);
    }

    public async Task<bool> ConnectToServerAsync(string serverPath)
    {
        var connection = await GetOrCreateConnectionAsync(serverPath);
        return connection != null;
    }

    public Task<bool> DisconnectFromServerAsync(string serverPath)
    {
        if (_connections.TryRemove(serverPath, out var connection))
        {
            try
            {
                if (connection.McpClient is IDisposable disposableClient)
                    disposableClient.Dispose();
                connection.Transport?.Dispose();
                connection.IsConnected = false;
                _logger.LogInformation("Disconnected from MCP server at {ServerPath}", serverPath);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from server at {ServerPath}", serverPath);
            }
        }
        return Task.FromResult(false);
    }

    public async Task<List<string>> GetAvailableToolsAsync(string serverPath)
    {
        var connection = await GetOrCreateConnectionAsync(serverPath);
        return connection?.AvailableTools ?? [];
    }

    public void Dispose()
    {
        foreach (var connection in _connections.Values)
        {
            try
            {
                if (connection.McpClient is IDisposable disposableClient)
                    disposableClient.Dispose();
                connection.Transport?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing connection");
            }
        }
        _connections.Clear();
        GC.SuppressFinalize(this);
    }

    private class ServerConnection
    {
        public object? McpClient { get; set; }
        public IDisposable? Transport { get; set; }
        public string ServerPath { get; set; } = string.Empty;
        public List<string> AvailableTools { get; set; } = [];
        public DateTime ConnectedAt { get; set; }
        public bool IsConnected { get; set; }
    }
}
