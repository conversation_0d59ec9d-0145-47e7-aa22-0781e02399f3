namespace McpClient.Configuration;

public class SearchConfiguration
{
    public string[] SearchKeywords { get; set; } = ["search for", "find", "look for", "show me", "get", "retrieve"];
    
    public string[] FileTypeKeywords { get; set; } = ["files", "documents", "cad", "drawings", "models"];
    
    public string[] CheckoutKeywords { get; set; } = ["checked out", "locked", "in use", "being edited"];
    
    public string[] RecentKeywords { get; set; } = ["recent", "latest", "new", "today", "yesterday", "this week"];
    
    public string[] UserKeywords { get; set; } = ["by", "from", "created by", "modified by", "user"];
    
    public string[] DateKeywords { get; set; } = ["since", "after", "before", "between", "on", "modified"];
    
    public Dictionary<string, string[]> FileTypeAliases { get; set; } = new()
    {
        ["cad"] = ["ipt", "iam", "dwg", "step", "stp"],
        ["inventor"] = ["ipt", "iam"],
        ["autocad"] = ["dwg", "dxf"],
        ["solidworks"] = ["sldprt", "sldasm"],
        ["documents"] = ["pdf", "doc", "docx", "txt"],
        ["images"] = ["jpg", "png", "gif", "bmp"]
    };
    
    public Dictionary<string, string> StateAliases { get; set; } = new()
    {
        ["released"] = "Released",
        ["wip"] = "Work in Progress",
        ["work in progress"] = "Work in Progress",
        ["draft"] = "Draft",
        ["obsolete"] = "Obsolete"
    };
}

public static class SearchConfigurationExtensions
{
    public static string? ExtractSearchQuery(this SearchConfiguration config, string questionLower)
    {
        foreach (var keyword in config.SearchKeywords)
        {
            var index = questionLower.IndexOf(keyword);
            if (index >= 0)
            {
                var afterKeyword = questionLower.Substring(index + keyword.Length).Trim();
                var words = afterKeyword.Split(' ').Take(3);
                return string.Join(" ", words);
            }
        }
        return null;
    }
    
    public static string? ExtractFileType(this SearchConfiguration config, string questionLower)
    {
        // Check for direct file extensions
        var extensionMatch = System.Text.RegularExpressions.Regex.Match(questionLower, @"\.(\w+)\b");
        if (extensionMatch.Success)
            return extensionMatch.Groups[1].Value;
        
        // Check for file type aliases
        foreach (var (alias, extensions) in config.FileTypeAliases)
        {
            if (questionLower.Contains(alias))
                return extensions.FirstOrDefault();
        }
        
        return null;
    }
    
    public static bool IsCheckoutQuery(this SearchConfiguration config, string questionLower)
    {
        return config.CheckoutKeywords.Any(keyword => questionLower.Contains(keyword));
    }
    
    public static bool IsRecentQuery(this SearchConfiguration config, string questionLower)
    {
        return config.RecentKeywords.Any(keyword => questionLower.Contains(keyword));
    }
    
    public static string? ExtractUserName(this SearchConfiguration config, string questionLower)
    {
        foreach (var keyword in config.UserKeywords)
        {
            var index = questionLower.IndexOf(keyword);
            if (index >= 0)
            {
                var afterKeyword = questionLower.Substring(index + keyword.Length).Trim();
                var userName = afterKeyword.Split(' ').FirstOrDefault();
                return userName;
            }
        }
        return null;
    }
    
    public static string? ExtractState(this SearchConfiguration config, string questionLower)
    {
        foreach (var (alias, state) in config.StateAliases)
        {
            if (questionLower.Contains(alias))
                return state;
        }
        return null;
    }
}
