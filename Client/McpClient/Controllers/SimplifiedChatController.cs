using Microsoft.AspNetCore.Mvc;
using McpClient.Services;

namespace McpClient.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SimplifiedChatController : ControllerBase
{
    private readonly SimplifiedMcpClientService _mcpService;

    public SimplifiedChatController(SimplifiedMcpClientService mcpService)
    {
        _mcpService = mcpService;
    }

    [HttpPost("ask")]
    public async Task<IActionResult> Ask([FromBody] SimplifiedAskRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Question))
        {
            return BadRequest("Question is required");
        }

        try
        {
            var response = await _mcpService.ProcessUserQuestionAsync(request.Question);
            return Ok(new { answer = response });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("tools")]
    public async Task<IActionResult> GetAvailableTools()
    {
        try
        {
            // This will fetch tools directly from MCP server
            var mcpService = new SimplifiedMcpClientService(null!); // Just for tool fetching
            var tools = await mcpService.GetAvailableToolsAsync();
            return Ok(new { tools });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = ex.Message });
        }
    }
}

public class SimplifiedAskRequest
{
    public string Question { get; set; } = string.Empty;
}

// Extension to make GetAvailableToolsAsync public for the controller
public static class SimplifiedMcpClientServiceExtensions
{
    public static async Task<List<ToolInfo>> GetAvailableToolsAsync(this SimplifiedMcpClientService service)
    {
        // Use reflection to call the private method
        var method = typeof(SimplifiedMcpClientService).GetMethod("GetAvailableToolsAsync", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (method != null)
        {
            var task = (Task<List<ToolInfo>>)method.Invoke(service, null)!;
            return await task;
        }
        
        return new List<ToolInfo>();
    }
}
