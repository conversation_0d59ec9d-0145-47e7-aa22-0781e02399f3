# Vault MCP Server (.NET)

A .NET implementation of the Model Context Protocol (MCP) server with general-purpose tools.

## Features

This MCP server provides the following tools:

### 🧮 Math Tools
- **calculate_math** - Perform mathematical calculations including basic arithmetic, trigonometry, and advanced functions
- **generate_statistics** - Calculate statistical measures for datasets (mean, median, mode, variance, etc.)

### 📝 Text Processing Tools
- **process_text** - Various text operations (word count, character count, transformations, word frequency, etc.)

### 🔧 Utility Tools
- **generate_uuid** - Generate UUIDs in v1 and v4 formats
- **generate_hash** - Create cryptographic hashes (MD5, SHA1, SHA256, SHA512)
- **generate_password** - Generate secure passwords with customizable options
- **convert_units** - Convert between different units (temperature, length, weight)

### ⏰ Time Tools
- **get_current_time** - Get current date and time in various formats
- **get_time_info** - Get detailed time information including timezone, day of week, etc.

### 🗄️ Vault Tools
- **get_vault_file_versions** - Get file versions from Autodesk Vault with filtering and pagination
- **get_vault_file_version** - Get a specific file version by ID
- **get_vault_files** - Get files from Autodesk Vault with optional filtering
- **get_vault_folders** - Get folders from Autodesk Vault with optional filtering
- **get_vault_info** - Get information about available Autodesk Vaults
- **search_vault_files** - Search for files with advanced filtering options

## Requirements

- .NET 8.0 or later

## Configuration

The server can be configured using environment variables:

- **VAULT_API_URL** - Base URL for the Vault API (default: `http://localhost:3000/AutodeskDM/Services/api/vault/v2`)
- **VAULT_AUTH_TOKEN** - Authentication token for the Vault API (default: `AuIPTf4KYLTYGVnOHQ0cuolwCW2a...`)
- **VAULT_ID** - Default Vault ID to use (default: `117`)

## Installation

1. Navigate to the ServerDotNet/VaultMcpServer directory
2. Restore dependencies:
   ```bash
   dotnet restore
   ```

## Usage

### Running the Server

```bash
cd ServerDotNet/VaultMcpServer
dotnet run
```

### Building the Server

```bash
cd ServerDotNet/VaultMcpServer
dotnet build
```

### Publishing the Server

```bash
cd ServerDotNet/VaultMcpServer
dotnet publish -c Release
```

## MCP Protocol

This server implements the Model Context Protocol (MCP) and communicates via JSON-RPC over stdio. It supports:

- Tool discovery via `tools/list`
- Tool execution via `tools/call`
- Standard MCP initialization

## Example Tool Calls

### Math Calculation
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "calculate_math",
    "arguments": {
      "expression": "sin(30) + cos(60)",
      "angleUnit": "degrees"
    }
  }
}
```

### Generate Statistics
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/call",
  "params": {
    "name": "generate_statistics",
    "arguments": {
      "numbers": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
      "includeAdvanced": true
    }
  }
}
```

### Text Processing
```json
{
  "jsonrpc": "2.0",
  "id": 3,
  "method": "tools/call",
  "params": {
    "name": "process_text",
    "arguments": {
      "text": "Hello World! This is a test.",
      "operation": "count_words"
    }
  }
}
```

### Vault API Call
```json
{
  "jsonrpc": "2.0",
  "id": 4,
  "method": "tools/call",
  "params": {
    "name": "get_vault_file_versions",
    "arguments": {
      "vaultId": "117",
      "limit": 5,
      "filter": "design"
    }
  }
}
```

## Architecture

The server is built with a modular architecture:

- **Models/** - MCP protocol models and data structures
- **Tools/** - Individual tool implementations
- **McpServer.cs** - Main server class handling MCP protocol
- **Program.cs** - Entry point

Each tool implements the `ITool` interface and can be easily added or removed from the server.

## Error Handling

The server includes comprehensive error handling:
- Invalid JSON requests
- Missing or invalid parameters
- Tool execution errors
- Protocol errors

All errors are returned as standard JSON-RPC error responses.
