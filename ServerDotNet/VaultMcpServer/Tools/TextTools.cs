using VaultMcpServer.Models;
using System.Text.RegularExpressions;
using System.Globalization;

namespace VaultMcpServer.Tools
{
    public class ProcessTextTool : BaseTool
    {
        public override string Name => "process_text";
        public override string Description => "Perform various text processing operations like counting, formatting, and analysis";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                text = new
                {
                    type = "string",
                    description = "Text to process"
                },
                operation = new
                {
                    type = "string",
                    @enum = new[] { 
                        "count_words", "count_characters", "count_lines", "reverse", 
                        "uppercase", "lowercase", "title_case", "word_frequency", 
                        "remove_duplicates", "sort_lines" 
                    },
                    description = "Operation to perform on the text"
                }
            },
            required = new[] { "text", "operation" }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var text = GetArgument<string>(arguments, "text", "");
                var operation = GetArgument<string>(arguments, "operation");

                if (string.IsNullOrEmpty(operation))
                    return CreateErrorResponse("Operation is required");

                var result = ProcessText(text, operation);
                return CreateSuccessResponse($"Operation: {operation}\nResult:\n{result}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error processing text: {ex.Message}");
            }
        }

        private string ProcessText(string text, string operation)
        {
            return operation switch
            {
                "count_words" => CountWords(text).ToString(),
                "count_characters" => $"Total: {text.Length}, Without spaces: {text.Replace(" ", "").Length}",
                "count_lines" => text.Split('\n').Length.ToString(),
                "reverse" => new string(text.Reverse().ToArray()),
                "uppercase" => text.ToUpper(),
                "lowercase" => text.ToLower(),
                "title_case" => CultureInfo.CurrentCulture.TextInfo.ToTitleCase(text.ToLower()),
                "word_frequency" => GetWordFrequency(text),
                "remove_duplicates" => RemoveDuplicateLines(text),
                "sort_lines" => SortLines(text),
                _ => throw new ArgumentException($"Unknown operation: {operation}")
            };
        }

        private int CountWords(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 0;

            return text.Trim().Split(new char[] { ' ', '\t', '\n', '\r' }, 
                StringSplitOptions.RemoveEmptyEntries).Length;
        }

        private string GetWordFrequency(string text)
        {
            var words = Regex.Matches(text.ToLower(), @"\b\w+\b")
                .Cast<Match>()
                .Select(m => m.Value)
                .GroupBy(w => w)
                .OrderByDescending(g => g.Count())
                .Take(10)
                .Select(g => $"{g.Key}: {g.Count()}");

            return string.Join("\n", words);
        }

        private string RemoveDuplicateLines(string text)
        {
            var lines = text.Split('\n');
            var uniqueLines = lines.Distinct();
            return string.Join("\n", uniqueLines);
        }

        private string SortLines(string text)
        {
            var lines = text.Split('\n');
            Array.Sort(lines);
            return string.Join("\n", lines);
        }
    }
}
