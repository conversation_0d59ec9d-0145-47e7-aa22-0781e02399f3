using VaultMcpServer.Models;
using System.Security.Cryptography;
using System.Text;

namespace VaultMcpServer.Tools
{
    public class GenerateUuidTool : BaseTool
    {
        public override string Name => "generate_uuid";
        public override string Description => "Generate UUIDs in various formats";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                version = new
                {
                    type = "string",
                    @enum = new[] { "v1", "v4" },
                    @default = "v4",
                    description = "UUID version to generate"
                },
                count = new
                {
                    type = "integer",
                    minimum = 1,
                    maximum = 100,
                    @default = 1,
                    description = "Number of UUIDs to generate"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var version = GetArgument<string>(arguments, "version", "v4");
                var count = GetArgument<int>(arguments, "count", 1);

                var uuids = new List<string>();
                for (int i = 0; i < count; i++)
                {
                    uuids.Add(version == "v4" ? Guid.NewGuid().ToString() : GenerateV1Uuid());
                }

                return CreateSuccessResponse($"Generated {count} UUID{(count > 1 ? "s" : "")} ({version}):\n{string.Join("\n", uuids)}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error generating UUID: {ex.Message}");
            }
        }

        private string GenerateV1Uuid()
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString("x");
            var random = Convert.ToHexString(RandomNumberGenerator.GetBytes(8)).ToLower();
            return $"{timestamp[..8]}-{timestamp[8..]}-1{random[..3]}-{random[3..7]}-{random[7..]}";
        }
    }

    public class GenerateHashTool : BaseTool
    {
        public override string Name => "generate_hash";
        public override string Description => "Generate cryptographic hashes of text using various algorithms";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                text = new
                {
                    type = "string",
                    description = "Text to hash"
                },
                algorithm = new
                {
                    type = "string",
                    @enum = new[] { "md5", "sha1", "sha256", "sha512" },
                    @default = "sha256",
                    description = "Hash algorithm to use"
                },
                encoding = new
                {
                    type = "string",
                    @enum = new[] { "hex", "base64" },
                    @default = "hex",
                    description = "Output encoding"
                }
            },
            required = new[] { "text" }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var text = GetArgument<string>(arguments, "text", "");
                var algorithm = GetArgument<string>(arguments, "algorithm", "sha256");
                var encoding = GetArgument<string>(arguments, "encoding", "hex");

                var hash = GenerateHash(text, algorithm, encoding);
                return CreateSuccessResponse($"Text: {text}\nAlgorithm: {algorithm}\nEncoding: {encoding}\nHash: {hash}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error generating hash: {ex.Message}");
            }
        }

        private string GenerateHash(string text, string algorithm, string encoding)
        {
            byte[] data = Encoding.UTF8.GetBytes(text);
            byte[] hash;

            HashAlgorithm hashAlgorithm = algorithm.ToLower() switch
            {
                "md5" => MD5.Create(),
                "sha1" => SHA1.Create(),
                "sha256" => SHA256.Create(),
                "sha512" => SHA512.Create(),
                _ => throw new ArgumentException($"Unsupported algorithm: {algorithm}")
            };

            using (hashAlgorithm)
            {
                hash = hashAlgorithm.ComputeHash(data);
            }

            return encoding.ToLower() switch
            {
                "hex" => Convert.ToHexString(hash).ToLower(),
                "base64" => Convert.ToBase64String(hash),
                _ => throw new ArgumentException($"Unsupported encoding: {encoding}")
            };
        }
    }

    public class GeneratePasswordTool : BaseTool
    {
        public override string Name => "generate_password";
        public override string Description => "Generate secure passwords with customizable options";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                length = new
                {
                    type = "integer",
                    minimum = 4,
                    maximum = 128,
                    @default = 16,
                    description = "Password length"
                },
                includeUppercase = new
                {
                    type = "boolean",
                    @default = true,
                    description = "Include uppercase letters"
                },
                includeLowercase = new
                {
                    type = "boolean",
                    @default = true,
                    description = "Include lowercase letters"
                },
                includeNumbers = new
                {
                    type = "boolean",
                    @default = true,
                    description = "Include numbers"
                },
                includeSymbols = new
                {
                    type = "boolean",
                    @default = false,
                    description = "Include symbols"
                },
                excludeSimilar = new
                {
                    type = "boolean",
                    @default = true,
                    description = "Exclude similar characters (0, O, l, 1, etc.)"
                },
                count = new
                {
                    type = "integer",
                    minimum = 1,
                    maximum = 20,
                    @default = 1,
                    description = "Number of passwords to generate"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var length = GetArgument<int>(arguments, "length", 16);
                var includeUppercase = GetArgument<bool>(arguments, "includeUppercase", true);
                var includeLowercase = GetArgument<bool>(arguments, "includeLowercase", true);
                var includeNumbers = GetArgument<bool>(arguments, "includeNumbers", true);
                var includeSymbols = GetArgument<bool>(arguments, "includeSymbols", false);
                var excludeSimilar = GetArgument<bool>(arguments, "excludeSimilar", true);
                var count = GetArgument<int>(arguments, "count", 1);

                var charset = BuildCharset(includeUppercase, includeLowercase, includeNumbers, includeSymbols, excludeSimilar);
                
                if (string.IsNullOrEmpty(charset))
                    return CreateErrorResponse("At least one character type must be selected");

                var passwords = new List<string>();
                for (int i = 0; i < count; i++)
                {
                    passwords.Add(GeneratePassword(length, charset));
                }

                var options = $"length: {length}, includeUppercase: {includeUppercase}, includeLowercase: {includeLowercase}, includeNumbers: {includeNumbers}, includeSymbols: {includeSymbols}, excludeSimilar: {excludeSimilar}";
                return CreateSuccessResponse($"Generated {count} password{(count > 1 ? "s" : "")} with options: {options}\n\n{string.Join("\n", passwords)}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error generating password: {ex.Message}");
            }
        }

        private string BuildCharset(bool includeUppercase, bool includeLowercase, bool includeNumbers, bool includeSymbols, bool excludeSimilar)
        {
            var charset = "";
            
            if (includeUppercase)
                charset += excludeSimilar ? "ABCDEFGHJKMNPQRSTUVWXYZ" : "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            if (includeLowercase)
                charset += excludeSimilar ? "abcdefghjkmnpqrstuvwxyz" : "abcdefghijklmnopqrstuvwxyz";
            if (includeNumbers)
                charset += excludeSimilar ? "23456789" : "0123456789";
            if (includeSymbols)
                charset += "!@#$%^&*()_+-=[]{}|;:,.<>?";

            return charset;
        }

        private string GeneratePassword(int length, string charset)
        {
            var password = new StringBuilder();
            using var rng = RandomNumberGenerator.Create();
            
            for (int i = 0; i < length; i++)
            {
                var randomBytes = new byte[4];
                rng.GetBytes(randomBytes);
                var randomIndex = Math.Abs(BitConverter.ToInt32(randomBytes, 0)) % charset.Length;
                password.Append(charset[randomIndex]);
            }

            return password.ToString();
        }
    }

    public class ConvertUnitsTool : BaseTool
    {
        public override string Name => "convert_units";
        public override string Description => "Convert between different units of measurement";

        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                value = new
                {
                    type = "number",
                    description = "Value to convert"
                },
                fromUnit = new
                {
                    type = "string",
                    description = "Source unit (e.g., 'celsius', 'fahrenheit', 'meters', 'feet', 'kg', 'lbs')"
                },
                toUnit = new
                {
                    type = "string",
                    description = "Target unit"
                },
                category = new
                {
                    type = "string",
                    @enum = new[] { "temperature", "length", "weight", "volume" },
                    description = "Unit category for validation"
                }
            },
            required = new[] { "value", "fromUnit", "toUnit" }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var value = GetArgument<double>(arguments, "value", 0);
                var fromUnit = GetArgument<string>(arguments, "fromUnit", "");
                var toUnit = GetArgument<string>(arguments, "toUnit", "");
                var category = GetArgument<string>(arguments, "category");

                var result = ConvertUnits(value, fromUnit, toUnit, category);
                return CreateSuccessResponse($"Conversion:\n{value} {fromUnit} = {result.result:F6} {toUnit} ({result.category})");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error converting units: {ex.Message}");
            }
        }

        private (double result, string category) ConvertUnits(double value, string fromUnit, string toUnit, string? category)
        {
            var conversions = new Dictionary<string, Dictionary<string, Dictionary<string, Func<double, double>>>>
            {
                ["temperature"] = new()
                {
                    ["celsius"] = new()
                    {
                        ["fahrenheit"] = c => (c * 9.0/5.0) + 32,
                        ["kelvin"] = c => c + 273.15
                    },
                    ["fahrenheit"] = new()
                    {
                        ["celsius"] = f => (f - 32) * 5.0/9.0,
                        ["kelvin"] = f => ((f - 32) * 5.0/9.0) + 273.15
                    },
                    ["kelvin"] = new()
                    {
                        ["celsius"] = k => k - 273.15,
                        ["fahrenheit"] = k => ((k - 273.15) * 9.0/5.0) + 32
                    }
                },
                ["length"] = new()
                {
                    ["meters"] = new()
                    {
                        ["feet"] = m => m * 3.28084,
                        ["inches"] = m => m * 39.3701,
                        ["kilometers"] = m => m / 1000,
                        ["miles"] = m => m / 1609.34
                    },
                    ["feet"] = new()
                    {
                        ["meters"] = ft => ft / 3.28084,
                        ["inches"] = ft => ft * 12,
                        ["kilometers"] = ft => ft / 3280.84,
                        ["miles"] = ft => ft / 5280
                    }
                },
                ["weight"] = new()
                {
                    ["kg"] = new()
                    {
                        ["lbs"] = kg => kg * 2.20462,
                        ["grams"] = kg => kg * 1000,
                        ["ounces"] = kg => kg * 35.274
                    },
                    ["lbs"] = new()
                    {
                        ["kg"] = lbs => lbs / 2.20462,
                        ["grams"] = lbs => lbs * 453.592,
                        ["ounces"] = lbs => lbs * 16
                    }
                }
            };

            string foundCategory = category ?? "";

            if (string.IsNullOrEmpty(foundCategory))
            {
                foreach (var (cat, units) in conversions)
                {
                    if (units.ContainsKey(fromUnit) && units[fromUnit].ContainsKey(toUnit))
                    {
                        foundCategory = cat;
                        break;
                    }
                }
            }

            if (string.IsNullOrEmpty(foundCategory) ||
                !conversions.ContainsKey(foundCategory) ||
                !conversions[foundCategory].ContainsKey(fromUnit) ||
                !conversions[foundCategory][fromUnit].ContainsKey(toUnit))
            {
                throw new ArgumentException($"Conversion from {fromUnit} to {toUnit} not supported");
            }

            var result = conversions[foundCategory][fromUnit][toUnit](value);
            return (result, foundCategory);
        }
    }
}
