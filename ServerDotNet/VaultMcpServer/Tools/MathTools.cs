using VaultMcpServer.Models;
using System.Data;
using System.Text.RegularExpressions;

namespace VaultMcpServer.Tools
{
    public class CalculateMathTool : BaseTool
    {
        public override string Name => "calculate_math";
        public override string Description => "Perform mathematical calculations including basic arithmetic, trigonometry, and advanced functions";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                expression = new
                {
                    type = "string",
                    description = "Mathematical expression to evaluate (e.g., '2 + 3 * 4', 'sin(30)', 'sqrt(16)')"
                },
                angleUnit = new
                {
                    type = "string",
                    @enum = new[] { "degrees", "radians" },
                    @default = "degrees",
                    description = "Unit for trigonometric functions"
                }
            },
            required = new[] { "expression" }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var expression = GetArgument<string>(arguments, "expression");
                var angleUnit = GetArgument<string>(arguments, "angleUnit", "degrees");

                if (string.IsNullOrEmpty(expression))
                    return CreateErrorResponse("Expression is required");

                var result = EvaluateMathExpression(expression, angleUnit);
                return CreateSuccessResponse($"Expression: {expression}\nResult: {result}\nAngle unit: {angleUnit}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error evaluating expression: {ex.Message}");
            }
        }

        private double EvaluateMathExpression(string expression, string angleUnit)
        {
            // Replace math functions with their implementations
            var processedExpression = expression;
            
            // Handle trigonometric functions
            if (angleUnit == "degrees")
            {
                processedExpression = Regex.Replace(processedExpression, @"sin\(([^)]+)\)", 
                    match => Math.Sin(double.Parse(match.Groups[1].Value) * Math.PI / 180).ToString());
                processedExpression = Regex.Replace(processedExpression, @"cos\(([^)]+)\)", 
                    match => Math.Cos(double.Parse(match.Groups[1].Value) * Math.PI / 180).ToString());
                processedExpression = Regex.Replace(processedExpression, @"tan\(([^)]+)\)", 
                    match => Math.Tan(double.Parse(match.Groups[1].Value) * Math.PI / 180).ToString());
            }
            else
            {
                processedExpression = Regex.Replace(processedExpression, @"sin\(([^)]+)\)", 
                    match => Math.Sin(double.Parse(match.Groups[1].Value)).ToString());
                processedExpression = Regex.Replace(processedExpression, @"cos\(([^)]+)\)", 
                    match => Math.Cos(double.Parse(match.Groups[1].Value)).ToString());
                processedExpression = Regex.Replace(processedExpression, @"tan\(([^)]+)\)", 
                    match => Math.Tan(double.Parse(match.Groups[1].Value)).ToString());
            }

            // Handle other math functions
            processedExpression = Regex.Replace(processedExpression, @"sqrt\(([^)]+)\)", 
                match => Math.Sqrt(double.Parse(match.Groups[1].Value)).ToString());
            processedExpression = Regex.Replace(processedExpression, @"pow\(([^,]+),([^)]+)\)", 
                match => Math.Pow(double.Parse(match.Groups[1].Value), double.Parse(match.Groups[2].Value)).ToString());
            processedExpression = Regex.Replace(processedExpression, @"log\(([^)]+)\)", 
                match => Math.Log(double.Parse(match.Groups[1].Value)).ToString());
            processedExpression = Regex.Replace(processedExpression, @"log10\(([^)]+)\)", 
                match => Math.Log10(double.Parse(match.Groups[1].Value)).ToString());
            processedExpression = Regex.Replace(processedExpression, @"exp\(([^)]+)\)", 
                match => Math.Exp(double.Parse(match.Groups[1].Value)).ToString());
            processedExpression = Regex.Replace(processedExpression, @"abs\(([^)]+)\)", 
                match => Math.Abs(double.Parse(match.Groups[1].Value)).ToString());

            // Replace constants
            processedExpression = processedExpression.Replace("pi", Math.PI.ToString());
            processedExpression = processedExpression.Replace("e", Math.E.ToString());

            // Use DataTable.Compute for basic arithmetic
            var table = new DataTable();
            var result = table.Compute(processedExpression, null);
            return Convert.ToDouble(result);
        }
    }

    public class GenerateStatisticsTool : BaseTool
    {
        public override string Name => "generate_statistics";
        public override string Description => "Calculate statistical measures for a dataset";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                numbers = new
                {
                    type = "array",
                    items = new { type = "number" },
                    description = "Array of numbers to analyze"
                },
                includeAdvanced = new
                {
                    type = "boolean",
                    @default = false,
                    description = "Include advanced statistics like skewness and kurtosis"
                }
            },
            required = new[] { "numbers" }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var numbers = GetArgumentArray<double>(arguments, "numbers");
                var includeAdvanced = GetArgument<bool>(arguments, "includeAdvanced", false);

                if (numbers == null || numbers.Count == 0)
                    return CreateErrorResponse("Dataset cannot be empty");

                var stats = CalculateStatistics(numbers, includeAdvanced);
                return CreateSuccessResponse($"Statistical Analysis:\n{stats}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error calculating statistics: {ex.Message}");
            }
        }

        private string CalculateStatistics(List<double> numbers, bool includeAdvanced)
        {
            var sorted = numbers.OrderBy(x => x).ToList();
            var n = numbers.Count;
            var sum = numbers.Sum();
            var mean = sum / n;

            // Basic statistics
            var variance = numbers.Sum(x => Math.Pow(x - mean, 2)) / n;
            var stdDev = Math.Sqrt(variance);
            var min = numbers.Min();
            var max = numbers.Max();
            var range = max - min;

            // Median
            var median = n % 2 == 0 
                ? (sorted[n/2 - 1] + sorted[n/2]) / 2 
                : sorted[n/2];

            // Quartiles
            var q1 = sorted[(int)(n * 0.25)];
            var q3 = sorted[(int)(n * 0.75)];
            var iqr = q3 - q1;

            var result = $"count: {n}\n" +
                        $"sum: {sum:F4}\n" +
                        $"mean: {mean:F4}\n" +
                        $"median: {median:F4}\n" +
                        $"min: {min:F4}\n" +
                        $"max: {max:F4}\n" +
                        $"range: {range:F4}\n" +
                        $"variance: {variance:F4}\n" +
                        $"standardDeviation: {stdDev:F4}\n" +
                        $"q1: {q1:F4}\n" +
                        $"q3: {q3:F4}\n" +
                        $"iqr: {iqr:F4}";

            if (includeAdvanced)
            {
                var skewness = numbers.Sum(x => Math.Pow((x - mean) / stdDev, 3)) / n;
                var kurtosis = numbers.Sum(x => Math.Pow((x - mean) / stdDev, 4)) / n - 3;
                
                result += $"\nskewness: {skewness:F4}\n" +
                         $"kurtosis: {kurtosis:F4}";
            }

            return result;
        }
    }
}
