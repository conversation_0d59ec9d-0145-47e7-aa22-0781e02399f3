using VaultMcpServer.Models;

namespace VaultMcpServer.Tools
{
    public interface ITool
    {
        string Name { get; }
        string Description { get; }
        object InputSchema { get; }
        Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments);
    }

    public abstract class BaseTool : ITool
    {
        public abstract string Name { get; }
        public abstract string Description { get; }
        public abstract object InputSchema { get; }

        public abstract Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments);

        protected ToolCallResponse CreateSuccessResponse(string text)
        {
            return new ToolCallResponse
            {
                Content = new List<ContentItem>
                {
                    new ContentItem { Type = "text", Text = text }
                },
                IsError = false
            };
        }

        protected ToolCallResponse CreateErrorResponse(string errorMessage)
        {
            return new ToolCallResponse
            {
                Content = new List<ContentItem>
                {
                    new ContentItem { Type = "text", Text = $"Error: {errorMessage}" }
                },
                IsError = true
            };
        }

        protected T? GetArgument<T>(Dictionary<string, object>? arguments, string key, T? defaultValue = default)
        {
            if (arguments == null || !arguments.ContainsKey(key))
                return defaultValue;

            try
            {
                var value = arguments[key];
                if (value is T directValue)
                    return directValue;

                if (typeof(T) == typeof(string))
                    return (T)(object)value.ToString()!;

                if (typeof(T) == typeof(int) || typeof(T) == typeof(int?))
                {
                    if (value is long longValue)
                        return (T)(object)(int)longValue;
                    if (int.TryParse(value.ToString(), out int intValue))
                        return (T)(object)intValue;
                }

                if (typeof(T) == typeof(double) || typeof(T) == typeof(double?))
                {
                    if (double.TryParse(value.ToString(), out double doubleValue))
                        return (T)(object)doubleValue;
                }

                if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                {
                    if (bool.TryParse(value.ToString(), out bool boolValue))
                        return (T)(object)boolValue;
                }

                return defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }

        protected List<T>? GetArgumentArray<T>(Dictionary<string, object>? arguments, string key)
        {
            if (arguments == null || !arguments.ContainsKey(key))
                return null;

            try
            {
                var value = arguments[key];
                if (value is System.Text.Json.JsonElement jsonElement && jsonElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                {
                    var result = new List<T>();
                    foreach (var item in jsonElement.EnumerateArray())
                    {
                        if (typeof(T) == typeof(double))
                        {
                            if (item.TryGetDouble(out double doubleVal))
                                result.Add((T)(object)doubleVal);
                        }
                        else if (typeof(T) == typeof(int))
                        {
                            if (item.TryGetInt32(out int intVal))
                                result.Add((T)(object)intVal);
                        }
                        else if (typeof(T) == typeof(string))
                        {
                            result.Add((T)(object)item.GetString()!);
                        }
                    }
                    return result;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }
    }
}
