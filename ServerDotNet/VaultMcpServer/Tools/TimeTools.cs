using VaultMcpServer.Models;

namespace VaultMcpServer.Tools
{
    public class GetCurrentTimeTool : BaseTool
    {
        public override string Name => "get_current_time";
        public override string Description => "Get the current date and time in various formats";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                format = new
                {
                    type = "string",
                    @enum = new[] { "iso", "local", "utc", "timestamp" },
                    @default = "iso",
                    description = "Time format to return"
                },
                timezone = new
                {
                    type = "string",
                    description = "Timezone (e.g., 'America/New_York', 'Europe/London')"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var format = GetArgument<string>(arguments, "format", "iso");
                var timezone = GetArgument<string>(arguments, "timezone");

                var now = DateTime.UtcNow;
                string result;

                switch (format)
                {
                    case "iso":
                        result = timezone != null ? 
                            TimeZoneInfo.ConvertTimeFromUtc(now, GetTimeZoneInfo(timezone)).ToString("yyyy-MM-ddTHH:mm:ss.fffZ") :
                            now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        break;
                    case "local":
                        result = timezone != null ?
                            TimeZoneInfo.ConvertTimeFromUtc(now, GetTimeZoneInfo(timezone)).ToString() :
                            now.ToLocalTime().ToString();
                        break;
                    case "utc":
                        result = now.ToString("R");
                        break;
                    case "timestamp":
                        result = ((DateTimeOffset)now).ToUnixTimeMilliseconds().ToString();
                        break;
                    default:
                        result = now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        break;
                }

                return CreateSuccessResponse($"Current time ({format}{(timezone != null ? $" in {timezone}" : "")}): {result}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error getting time: {ex.Message}");
            }
        }

        private TimeZoneInfo GetTimeZoneInfo(string timezone)
        {
            try
            {
                return TimeZoneInfo.FindSystemTimeZoneById(timezone);
            }
            catch
            {
                // Try common timezone mappings
                var mappings = new Dictionary<string, string>
                {
                    ["America/New_York"] = "Eastern Standard Time",
                    ["America/Chicago"] = "Central Standard Time",
                    ["America/Denver"] = "Mountain Standard Time",
                    ["America/Los_Angeles"] = "Pacific Standard Time",
                    ["Europe/London"] = "GMT Standard Time",
                    ["Europe/Paris"] = "W. Europe Standard Time",
                    ["Asia/Tokyo"] = "Tokyo Standard Time"
                };

                if (mappings.ContainsKey(timezone))
                {
                    return TimeZoneInfo.FindSystemTimeZoneById(mappings[timezone]);
                }

                throw new ArgumentException($"Unknown timezone: {timezone}");
            }
        }
    }

    public class GetTimeInfoTool : BaseTool
    {
        public override string Name => "get_time_info";
        public override string Description => "Get detailed time information including timezone, day of week, etc.";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                timezone = new
                {
                    type = "string",
                    description = "Timezone (e.g., 'America/New_York', 'Europe/London')"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var timezone = GetArgument<string>(arguments, "timezone");
                var now = DateTime.UtcNow;
                
                DateTime localTime = timezone != null ? 
                    TimeZoneInfo.ConvertTimeFromUtc(now, GetTimeZoneInfo(timezone)) : 
                    now.ToLocalTime();

                var timeInfo = new Dictionary<string, object>
                {
                    ["iso"] = now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    ["local"] = localTime.ToString(),
                    ["utc"] = now.ToString("R"),
                    ["timestamp"] = ((DateTimeOffset)now).ToUnixTimeMilliseconds(),
                    ["dayOfWeek"] = localTime.DayOfWeek.ToString(),
                    ["month"] = localTime.ToString("MMMM"),
                    ["year"] = localTime.Year,
                    ["timezone"] = timezone ?? TimeZoneInfo.Local.Id
                };

                var formattedInfo = string.Join("\n", timeInfo.Select(kvp => $"{kvp.Key}: {kvp.Value}"));
                return CreateSuccessResponse($"Time Information:\n{formattedInfo}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse($"Error getting time info: {ex.Message}");
            }
        }

        private TimeZoneInfo GetTimeZoneInfo(string timezone)
        {
            try
            {
                return TimeZoneInfo.FindSystemTimeZoneById(timezone);
            }
            catch
            {
                var mappings = new Dictionary<string, string>
                {
                    ["America/New_York"] = "Eastern Standard Time",
                    ["America/Chicago"] = "Central Standard Time",
                    ["America/Denver"] = "Mountain Standard Time",
                    ["America/Los_Angeles"] = "Pacific Standard Time",
                    ["Europe/London"] = "GMT Standard Time",
                    ["Europe/Paris"] = "W. Europe Standard Time",
                    ["Asia/Tokyo"] = "Tokyo Standard Time"
                };

                if (mappings.ContainsKey(timezone))
                {
                    return TimeZoneInfo.FindSystemTimeZoneById(mappings[timezone]);
                }

                throw new ArgumentException($"Unknown timezone: {timezone}");
            }
        }
    }
}
