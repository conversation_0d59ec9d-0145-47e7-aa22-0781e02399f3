using VaultMcpServer.Models;
using System.Text.Json;
using System.Text;

namespace VaultMcpServer.Tools
{
    public class VaultConfig
    {
        public static string BaseUrl => Environment.GetEnvironmentVariable("VAULT_API_URL") ?? "http://localhost:4000/AutodeskDM/Services/api/vault/v2";
        public static string AuthToken => Environment.GetEnvironmentVariable("VAULT_AUTH_TOKEN") ?? "AuIPTf4KYLTYGVnOHQ0cuolwCW2a...";
        public static string DefaultVaultId => Environment.GetEnvironmentVariable("VAULT_ID") ?? "117";
    }

    public abstract class BaseVaultTool : BaseTool
    {
        protected readonly HttpClient _httpClient;
        protected readonly JsonSerializerOptions _jsonOptions;

        protected BaseVaultTool()
        {
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri(VaultConfig.BaseUrl),
                Timeout = TimeSpan.FromSeconds(30)
            };
            
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {VaultConfig.AuthToken}");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };
        }

        protected string FormatVaultResponse(object? data, string operation)
        {
            if (data == null)
                return $"No data returned from Vault API for {operation}";

            return JsonSerializer.Serialize(data, _jsonOptions);
        }

        protected string HandleVaultError(Exception error, string operation)
        {
            if (error is HttpRequestException httpError)
            {
                return $"Network Error: Unable to connect to Vault API during {operation}. Check if the Vault server is running. Details: {httpError.Message}";
            }
            
            return $"Error during {operation}: {error.Message}";
        }

        protected async Task<T?> MakeVaultApiCall<T>(string endpoint)
        {
            try
            {
                var response = await _httpClient.GetAsync(endpoint);
                response.EnsureSuccessStatusCode();
                
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(content, _jsonOptions);
            }
            catch (HttpRequestException ex) when (ex.Message.Contains("404"))
            {
                throw new Exception($"Vault API endpoint not found: {endpoint}");
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"Vault API request failed: {ex.Message}");
            }
        }
    }

    public class GetVaultFileVersionsTool : BaseVaultTool
    {
        public override string Name => "get_vault_file_versions";
        public override string Description => "Get file versions from Autodesk Vault with optional filtering and pagination";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                vaultId = new
                {
                    type = "string",
                    @default = VaultConfig.DefaultVaultId,
                    description = "Vault ID to query"
                },
                limit = new
                {
                    type = "integer",
                    minimum = 1,
                    maximum = 100,
                    @default = 10,
                    description = "Number of results to return (1-100)"
                },
                filter = new
                {
                    type = "string",
                    description = "Text filter for file names, categories, or states"
                },
                category = new
                {
                    type = "string",
                    description = "Filter by specific category (e.g., 'Base', 'Design')"
                },
                state = new
                {
                    type = "string",
                    description = "Filter by file state (e.g., 'Released', 'Work in Progress')"
                },
                isCheckedOut = new
                {
                    type = "boolean",
                    description = "Filter by checkout status"
                },
                orderBy = new
                {
                    type = "string",
                    @default = "lastModifiedDate",
                    description = "Field to sort by"
                },
                orderDirection = new
                {
                    type = "string",
                    @enum = new[] { "asc", "desc" },
                    @default = "desc",
                    description = "Sort direction"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var vaultId = GetArgument<string>(arguments, "vaultId", VaultConfig.DefaultVaultId);
                var limit = GetArgument<int>(arguments, "limit", 10);
                var filter = GetArgument<string>(arguments, "filter");
                var category = GetArgument<string>(arguments, "category");
                var state = GetArgument<string>(arguments, "state");
                var isCheckedOut = GetArgument<bool?>(arguments, "isCheckedOut");
                var orderBy = GetArgument<string>(arguments, "orderBy", "lastModifiedDate");
                var orderDirection = GetArgument<string>(arguments, "orderDirection", "desc");

                var queryParams = new List<string> { $"limit={limit}" };
                
                if (!string.IsNullOrEmpty(filter)) queryParams.Add($"filter={Uri.EscapeDataString(filter)}");
                if (!string.IsNullOrEmpty(category)) queryParams.Add($"category={Uri.EscapeDataString(category)}");
                if (!string.IsNullOrEmpty(state)) queryParams.Add($"state={Uri.EscapeDataString(state)}");
                if (isCheckedOut.HasValue) queryParams.Add($"isCheckedOut={isCheckedOut.Value.ToString().ToLower()}");
                if (orderBy != "lastModifiedDate") queryParams.Add($"orderBy={orderBy}");
                if (orderDirection != "desc") queryParams.Add($"orderDirection={orderDirection}");

                var endpoint = $"/vaults/{vaultId}/file-versions?{string.Join("&", queryParams)}";
                var data = await MakeVaultApiCall<object>(endpoint);

                // Extract result count for summary
                var dataJson = JsonSerializer.Serialize(data, _jsonOptions);
                var dataDict = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson);
                var resultCount = 0;
                var totalResults = 0;

                if (dataDict?.ContainsKey("results") == true && dataDict["results"] is JsonElement resultsElement)
                {
                    if (resultsElement.ValueKind == JsonValueKind.Array)
                        resultCount = resultsElement.GetArrayLength();
                }

                if (dataDict?.ContainsKey("pagination") == true && dataDict["pagination"] is JsonElement paginationElement)
                {
                    if (paginationElement.TryGetProperty("totalResults", out var totalElement))
                        totalResults = totalElement.GetInt32();
                }

                var summary = $"Found {resultCount} file versions ({totalResults} total)";
                if (!string.IsNullOrEmpty(filter) || !string.IsNullOrEmpty(category) || !string.IsNullOrEmpty(state) || isCheckedOut.HasValue)
                {
                    var filters = new List<string>();
                    if (!string.IsNullOrEmpty(filter)) filters.Add($"text: \"{filter}\"");
                    if (!string.IsNullOrEmpty(category)) filters.Add($"category: \"{category}\"");
                    if (!string.IsNullOrEmpty(state)) filters.Add($"state: \"{state}\"");
                    if (isCheckedOut.HasValue) filters.Add($"checked out: {isCheckedOut.Value}");
                    summary += $" with filters: {string.Join(", ", filters)}";
                }

                return CreateSuccessResponse($"{summary}\n\n{FormatVaultResponse(data, "get file versions")}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "get file versions"));
            }
        }
    }

    public class GetVaultFileVersionTool : BaseVaultTool
    {
        public override string Name => "get_vault_file_version";
        public override string Description => "Get a specific file version from Autodesk Vault by ID";
        
        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                vaultId = new
                {
                    type = "string",
                    @default = VaultConfig.DefaultVaultId,
                    description = "Vault ID"
                },
                fileVersionId = new
                {
                    type = "string",
                    description = "File version ID to retrieve"
                }
            },
            required = new[] { "fileVersionId" }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var vaultId = GetArgument<string>(arguments, "vaultId", VaultConfig.DefaultVaultId);
                var fileVersionId = GetArgument<string>(arguments, "fileVersionId");

                if (string.IsNullOrEmpty(fileVersionId))
                    return CreateErrorResponse("File version ID is required");

                var endpoint = $"/vaults/{vaultId}/file-versions/{fileVersionId}";
                var data = await MakeVaultApiCall<object>(endpoint);

                // Extract file details for summary
                var dataJson = JsonSerializer.Serialize(data, _jsonOptions);
                var dataDict = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson);
                
                var fileName = "Unknown";
                var version = "Unknown";
                var state = "No state";

                if (dataDict?.ContainsKey("name") == true && dataDict["name"] is JsonElement nameElement)
                    fileName = nameElement.GetString() ?? "Unknown";
                
                if (dataDict?.ContainsKey("version") == true && dataDict["version"] is JsonElement versionElement)
                    version = versionElement.GetString() ?? "Unknown";
                
                if (dataDict?.ContainsKey("state") == true && dataDict["state"] is JsonElement stateElement)
                    state = stateElement.GetString() ?? "No state";

                return CreateSuccessResponse($"File Version Details for \"{fileName}\" (v{version}, state: {state})\n\n{FormatVaultResponse(data, "get file version")}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "get file version"));
            }
        }
    }

    public class GetVaultFilesTool : BaseVaultTool
    {
        public override string Name => "get_vault_files";
        public override string Description => "Get files from Autodesk Vault with optional filtering";

        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                vaultId = new
                {
                    type = "string",
                    @default = VaultConfig.DefaultVaultId,
                    description = "Vault ID to query"
                },
                limit = new
                {
                    type = "integer",
                    minimum = 1,
                    maximum = 100,
                    @default = 10,
                    description = "Number of results to return"
                },
                filter = new
                {
                    type = "string",
                    description = "Text filter for file names"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var vaultId = GetArgument<string>(arguments, "vaultId", VaultConfig.DefaultVaultId);
                var limit = GetArgument<int>(arguments, "limit", 10);
                var filter = GetArgument<string>(arguments, "filter");

                var queryParams = new List<string> { $"limit={limit}" };
                if (!string.IsNullOrEmpty(filter)) queryParams.Add($"filter={Uri.EscapeDataString(filter)}");

                var endpoint = $"/vaults/{vaultId}/files?{string.Join("&", queryParams)}";
                var data = await MakeVaultApiCall<object>(endpoint);

                // Extract result count for summary
                var dataJson = JsonSerializer.Serialize(data, _jsonOptions);
                var dataDict = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson);
                var resultCount = 0;

                if (dataDict?.ContainsKey("results") == true && dataDict["results"] is JsonElement resultsElement)
                {
                    if (resultsElement.ValueKind == JsonValueKind.Array)
                        resultCount = resultsElement.GetArrayLength();
                }

                var summary = $"Found {resultCount} files";
                if (!string.IsNullOrEmpty(filter))
                    summary += $" matching \"{filter}\"";

                return CreateSuccessResponse($"{summary}\n\n{FormatVaultResponse(data, "get files")}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "get files"));
            }
        }
    }

    public class GetVaultFoldersTool : BaseVaultTool
    {
        public override string Name => "get_vault_folders";
        public override string Description => "Get folders from Autodesk Vault with optional filtering";

        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                vaultId = new
                {
                    type = "string",
                    @default = VaultConfig.DefaultVaultId,
                    description = "Vault ID to query"
                },
                limit = new
                {
                    type = "integer",
                    minimum = 1,
                    maximum = 100,
                    @default = 10,
                    description = "Number of results to return"
                },
                filter = new
                {
                    type = "string",
                    description = "Text filter for folder names"
                },
                parentId = new
                {
                    type = "string",
                    description = "Parent folder ID to filter by"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var vaultId = GetArgument<string>(arguments, "vaultId", VaultConfig.DefaultVaultId);
                var limit = GetArgument<int>(arguments, "limit", 10);
                var filter = GetArgument<string>(arguments, "filter");
                var parentId = GetArgument<string>(arguments, "parentId");

                var queryParams = new List<string> { $"limit={limit}" };
                if (!string.IsNullOrEmpty(filter)) queryParams.Add($"filter={Uri.EscapeDataString(filter)}");
                if (!string.IsNullOrEmpty(parentId)) queryParams.Add($"parentId={parentId}");

                var endpoint = $"/vaults/{vaultId}/folders?{string.Join("&", queryParams)}";
                var data = await MakeVaultApiCall<object>(endpoint);

                // Extract result count for summary
                var dataJson = JsonSerializer.Serialize(data, _jsonOptions);
                var dataDict = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson);
                var resultCount = 0;

                if (dataDict?.ContainsKey("results") == true && dataDict["results"] is JsonElement resultsElement)
                {
                    if (resultsElement.ValueKind == JsonValueKind.Array)
                        resultCount = resultsElement.GetArrayLength();
                }

                var summary = $"Found {resultCount} folders";
                if (!string.IsNullOrEmpty(filter)) summary += $" matching \"{filter}\"";
                if (!string.IsNullOrEmpty(parentId)) summary += $" in parent folder {parentId}";

                return CreateSuccessResponse($"{summary}\n\n{FormatVaultResponse(data, "get folders")}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "get folders"));
            }
        }
    }

    public class GetVaultInfoTool : BaseVaultTool
    {
        public override string Name => "get_vault_info";
        public override string Description => "Get information about available Autodesk Vaults";

        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                vaultId = new
                {
                    type = "string",
                    description = "Specific vault ID to get info for, or leave empty for all vaults"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var vaultId = GetArgument<string>(arguments, "vaultId");
                var endpoint = !string.IsNullOrEmpty(vaultId) ? $"/vaults/{vaultId}" : "/vaults";
                var data = await MakeVaultApiCall<object>(endpoint);

                if (!string.IsNullOrEmpty(vaultId))
                {
                    return CreateSuccessResponse($"Vault Information for ID {vaultId}\n\n{FormatVaultResponse(data, "get vault info")}");
                }
                else
                {
                    // Extract vault count for summary
                    var dataJson = JsonSerializer.Serialize(data, _jsonOptions);
                    var dataDict = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson);
                    var vaultCount = 0;

                    if (dataDict?.ContainsKey("results") == true && dataDict["results"] is JsonElement resultsElement)
                    {
                        if (resultsElement.ValueKind == JsonValueKind.Array)
                            vaultCount = resultsElement.GetArrayLength();
                    }

                    return CreateSuccessResponse($"Found {vaultCount} available vaults\n\n{FormatVaultResponse(data, "get vaults")}");
                }
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "get vault info"));
            }
        }
    }

    public class SearchVaultFilesTool : BaseVaultTool
    {
        public override string Name => "search_vault_files";
        public override string Description => "Search for files in Autodesk Vault with advanced filtering options";

        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                query = new
                {
                    type = "string",
                    description = "Search query for file names, categories, or states"
                },
                vaultId = new
                {
                    type = "string",
                    @default = VaultConfig.DefaultVaultId,
                    description = "Vault ID to search in"
                },
                limit = new
                {
                    type = "integer",
                    minimum = 1,
                    maximum = 50,
                    @default = 20,
                    description = "Maximum number of results"
                },
                fileType = new
                {
                    type = "string",
                    description = "Filter by file extension (e.g., 'ipt', 'iam', 'dwg')"
                },
                checkedOutOnly = new
                {
                    type = "boolean",
                    description = "Only show checked out files"
                },
                recentOnly = new
                {
                    type = "boolean",
                    description = "Only show recently modified files"
                }
            },
            required = new[] { "query" }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var query = GetArgument<string>(arguments, "query", "");
                var vaultId = GetArgument<string>(arguments, "vaultId", VaultConfig.DefaultVaultId);
                var limit = GetArgument<int>(arguments, "limit", 20);
                var fileType = GetArgument<string>(arguments, "fileType");
                var checkedOutOnly = GetArgument<bool?>(arguments, "checkedOutOnly");
                var recentOnly = GetArgument<bool?>(arguments, "recentOnly");

                var queryParams = new List<string> { $"limit={limit}", $"filter={Uri.EscapeDataString(query)}" };

                // Send all filter parameters to API
                if (!string.IsNullOrEmpty(fileType)) queryParams.Add($"fileType={Uri.EscapeDataString(fileType)}");
                if (checkedOutOnly == true) queryParams.Add("isCheckedOut=true");
                if (recentOnly == true)
                {
                    queryParams.Add("orderBy=lastModifiedDate");
                    queryParams.Add("orderDirection=desc");
                }

                var endpoint = $"/vaults/{vaultId}/file-versions?{string.Join("&", queryParams)}";
                var data = await MakeVaultApiCall<object>(endpoint);

                var dataJson = JsonSerializer.Serialize(data, _jsonOptions);
                var dataDict = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson);
                var resultCount = 0;

                if (dataDict?.ContainsKey("results") == true && dataDict["results"] is JsonElement resultsElement)
                {
                    if (resultsElement.ValueKind == JsonValueKind.Array)
                        resultCount = resultsElement.GetArrayLength();
                }

                var summary = $"Search Results: Found {resultCount} files";
                if (!string.IsNullOrEmpty(fileType)) summary += $" ({fileType} files only)";
                if (checkedOutOnly == true) summary += " (checked out only)";
                if (recentOnly == true) summary += " (recent files first)";
                summary += $" matching \"{query}\"";

                return CreateSuccessResponse($"{summary}\n\n{FormatVaultResponse(data, "search files")}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "search files"));
            }
        }
    }

    public class GetVaultFilesByDateRangeTool : BaseVaultTool
    {
        public override string Name => "get_vault_files_by_date_range";
        public override string Description => "Get files modified within a specific date range";

        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                vaultId = new
                {
                    type = "string",
                    @default = VaultConfig.DefaultVaultId,
                    description = "Vault ID to query"
                },
                startDate = new
                {
                    type = "string",
                    description = "Start date in ISO format (e.g., '2024-01-01T00:00:00Z')"
                },
                endDate = new
                {
                    type = "string",
                    description = "End date in ISO format (defaults to current time)"
                },
                limit = new
                {
                    type = "integer",
                    minimum = 1,
                    maximum = 100,
                    @default = 20,
                    description = "Maximum number of results"
                },
                orderDirection = new
                {
                    type = "string",
                    @enum = new[] { "asc", "desc" },
                    @default = "desc",
                    description = "Sort direction by modification date"
                }
            },
            required = new[] { "startDate" }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var vaultId = GetArgument<string>(arguments, "vaultId", VaultConfig.DefaultVaultId);
                var startDate = GetArgument<string>(arguments, "startDate");
                var endDate = GetArgument<string>(arguments, "endDate");
                var limit = GetArgument<int>(arguments, "limit", 20);
                var orderDirection = GetArgument<string>(arguments, "orderDirection", "desc");

                if (string.IsNullOrEmpty(startDate))
                    return CreateErrorResponse("Start date is required");

                var queryParams = new List<string>
                {
                    $"limit={limit}",
                    "orderBy=lastModifiedDate",
                    $"orderDirection={orderDirection}",
                    $"startDate={Uri.EscapeDataString(startDate)}"
                };

                if (!string.IsNullOrEmpty(endDate))
                    queryParams.Add($"endDate={Uri.EscapeDataString(endDate)}");

                var endpoint = $"/vaults/{vaultId}/file-versions?{string.Join("&", queryParams)}";
                var data = await MakeVaultApiCall<object>(endpoint);

                // Extract result count for summary
                var dataJson = JsonSerializer.Serialize(data, _jsonOptions);
                var dataDict = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson);
                var resultCount = 0;

                if (dataDict?.ContainsKey("results") == true && dataDict["results"] is JsonElement resultsElement)
                {
                    if (resultsElement.ValueKind == JsonValueKind.Array)
                        resultCount = resultsElement.GetArrayLength();
                }

                var start = DateTime.Parse(startDate);
                var end = !string.IsNullOrEmpty(endDate) ? DateTime.Parse(endDate) : DateTime.Now;
                var dateRangeText = !string.IsNullOrEmpty(endDate)
                    ? $"between {start:yyyy-MM-dd} and {end:yyyy-MM-dd}"
                    : $"since {start:yyyy-MM-dd}";

                return CreateSuccessResponse($"Found {resultCount} files modified {dateRangeText}\n\n{FormatVaultResponse(data, "get files by date range")}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "get files by date range"));
            }
        }
    }

    public class GetVaultFileStatsTool : BaseVaultTool
    {
        public override string Name => "get_vault_file_stats";
        public override string Description => "Get statistics about files in the vault (counts, sizes, types, etc.)";

        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                vaultId = new
                {
                    type = "string",
                    @default = VaultConfig.DefaultVaultId,
                    description = "Vault ID to analyze"
                },
                includeDetails = new
                {
                    type = "boolean",
                    @default = false,
                    description = "Include detailed breakdown by file type"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var vaultId = GetArgument<string>(arguments, "vaultId", VaultConfig.DefaultVaultId);
                var includeDetails = GetArgument<bool>(arguments, "includeDetails", false);

                var endpoint = $"/vaults/{vaultId}/file-versions?limit=100";
                var data = await MakeVaultApiCall<object>(endpoint);

                // This would typically be processed by the API server
                // For now, we'll return the raw data and let the API handle statistics
                var summary = includeDetails
                    ? "Vault Statistics (detailed breakdown requested)"
                    : "Vault Statistics (summary)";

                return CreateSuccessResponse($"{summary}\n\n{FormatVaultResponse(data, "get vault statistics")}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "get vault statistics"));
            }
        }
    }

    public class GetVaultUserActivityTool : BaseVaultTool
    {
        public override string Name => "get_vault_user_activity";
        public override string Description => "Get user activity information (who checked out what, recent modifications)";

        public override object InputSchema => new
        {
            type = "object",
            properties = new
            {
                vaultId = new
                {
                    type = "string",
                    @default = VaultConfig.DefaultVaultId,
                    description = "Vault ID to query"
                },
                userName = new
                {
                    type = "string",
                    description = "Specific user to filter by"
                },
                limit = new
                {
                    type = "integer",
                    minimum = 1,
                    maximum = 50,
                    @default = 20,
                    description = "Maximum number of results"
                }
            }
        };

        public override async Task<ToolCallResponse> ExecuteAsync(Dictionary<string, object>? arguments)
        {
            try
            {
                var vaultId = GetArgument<string>(arguments, "vaultId", VaultConfig.DefaultVaultId);
                var userName = GetArgument<string>(arguments, "userName");
                var limit = GetArgument<int>(arguments, "limit", 20);

                var queryParams = new List<string>
                {
                    $"limit={limit}",
                    "orderBy=lastModifiedDate",
                    "orderDirection=desc"
                };

                if (!string.IsNullOrEmpty(userName))
                    queryParams.Add($"userName={Uri.EscapeDataString(userName)}");

                var endpoint = $"/vaults/{vaultId}/file-versions?{string.Join("&", queryParams)}";
                var data = await MakeVaultApiCall<object>(endpoint);

                var summary = !string.IsNullOrEmpty(userName)
                    ? $"User Activity for {userName}"
                    : "User Activity Summary";

                return CreateSuccessResponse($"{summary}\n\n{FormatVaultResponse(data, "get user activity")}");
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(HandleVaultError(ex, "get user activity"));
            }
        }
    }
}
