using System.Text.Json;
using VaultMcpServer.Models;
using VaultMcpServer.Tools;

namespace VaultMcpServer
{
    public class McpServer
    {
        private readonly Dictionary<string, ITool> _tools;
        private readonly JsonSerializerOptions _jsonOptions;

        public McpServer()
        {
            _tools = new Dictionary<string, ITool>();
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };

            RegisterTools();
        }

        private void RegisterTools()
        {
            // Essential tools only
            RegisterTool(new CalculateMathTool());
            RegisterTool(new ProcessTextTool());
            RegisterTool(new GetCurrentTimeTool());

            // Vault tools
            RegisterTool(new GetVaultFileVersionsTool());
            RegisterTool(new GetVaultFileVersionTool());
            RegisterTool(new GetVaultFilesTool());
            RegisterTool(new GetVaultFoldersTool());
            RegisterTool(new GetVaultInfoTool());
            RegisterTool(new SearchVaultFilesTool());

            // Advanced Vault tools
            RegisterTool(new GetVaultFilesByDateRangeTool());
            RegisterTool(new GetVaultFileStatsTool());
            RegisterTool(new GetVaultUserActivityTool());

            Console.Error.WriteLine($"Registered tools: {string.Join(", ", _tools.Keys)}");
        }

        private void RegisterTool(ITool tool)
        {
            _tools[tool.Name] = tool;
        }

        public async Task RunAsync()
        {
            Console.Error.WriteLine("MCP Server running with general-purpose tools");

            while (true)
            {
                try
                {
                    var line = await Console.In.ReadLineAsync();
                    if (line == null) break;

                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var request = JsonSerializer.Deserialize<McpRequest>(line, _jsonOptions);
                    if (request == null) continue;

                    var response = await HandleRequestAsync(request);
                    var responseJson = JsonSerializer.Serialize(response, _jsonOptions);
                    
                    await Console.Out.WriteLineAsync(responseJson);
                    await Console.Out.FlushAsync();
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Error processing request: {ex.Message}");
                    
                    var errorResponse = new McpResponse
                    {
                        Id = null,
                        Error = new McpError
                        {
                            Code = -32603,
                            Message = "Internal error",
                            Data = ex.Message
                        }
                    };

                    var errorJson = JsonSerializer.Serialize(errorResponse, _jsonOptions);
                    await Console.Out.WriteLineAsync(errorJson);
                    await Console.Out.FlushAsync();
                }
            }
        }

        private async Task<McpResponse> HandleRequestAsync(McpRequest request)
        {
            try
            {
                return request.Method switch
                {
                    "initialize" => HandleInitialize(request),
                    "tools/list" => HandleListTools(request),
                    "tools/call" => await HandleToolCallAsync(request),
                    _ => CreateErrorResponse(request.Id, -32601, "Method not found")
                };
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(request.Id, -32603, "Internal error", ex.Message);
            }
        }

        private McpResponse HandleInitialize(McpRequest request)
        {
            var response = new InitializeResponse
            {
                ProtocolVersion = "2024-11-05",
                Capabilities = new ServerCapabilities
                {
                    Tools = new { },
                    Resources = new { }
                },
                ServerInfo = new ServerInfo
                {
                    Name = "vault-mcp-server-dotnet",
                    Version = "1.0.0"
                }
            };

            return new McpResponse
            {
                Id = request.Id,
                Result = response
            };
        }

        private McpResponse HandleListTools(McpRequest request)
        {
            var tools = _tools.Values.Select(tool => new ToolDefinition
            {
                Name = tool.Name,
                Description = tool.Description,
                InputSchema = tool.InputSchema
            }).ToList();

            var response = new ListToolsResponse
            {
                Tools = tools
            };

            return new McpResponse
            {
                Id = request.Id,
                Result = response
            };
        }

        private async Task<McpResponse> HandleToolCallAsync(McpRequest request)
        {
            try
            {
                if (request.Params == null)
                    return CreateErrorResponse(request.Id, -32602, "Invalid params");

                var paramsElement = (JsonElement)request.Params;
                var toolCallRequest = JsonSerializer.Deserialize<ToolCallRequest>(paramsElement.GetRawText(), _jsonOptions);

                if (toolCallRequest == null || string.IsNullOrEmpty(toolCallRequest.Name))
                    return CreateErrorResponse(request.Id, -32602, "Invalid tool call request");

                if (!_tools.ContainsKey(toolCallRequest.Name))
                    return CreateErrorResponse(request.Id, -32601, $"Tool '{toolCallRequest.Name}' not found");

                var tool = _tools[toolCallRequest.Name];
                var result = await tool.ExecuteAsync(toolCallRequest.Arguments);

                return new McpResponse
                {
                    Id = request.Id,
                    Result = result
                };
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(request.Id, -32603, "Tool execution error", ex.Message);
            }
        }

        private McpResponse CreateErrorResponse(object? id, int code, string message, string? data = null)
        {
            return new McpResponse
            {
                Id = id,
                Error = new McpError
                {
                    Code = code,
                    Message = message,
                    Data = data
                }
            };
        }
    }
}
