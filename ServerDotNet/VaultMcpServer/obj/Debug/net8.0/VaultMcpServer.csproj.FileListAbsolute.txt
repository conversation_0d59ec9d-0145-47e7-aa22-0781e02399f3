/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcpServer.csproj.AssemblyReference.cache
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcpServer.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcpServer.AssemblyInfoInputs.cache
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcpServer.AssemblyInfo.cs
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcpServer.csproj.CoreCompileInputs.cache
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/VaultMcpServer
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/VaultMcpServer.deps.json
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/VaultMcpServer.runtimeconfig.json
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/VaultMcpServer.dll
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/VaultMcpServer.pdb
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/Newtonsoft.Json.dll
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/System.IO.Pipelines.dll
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/System.Text.Json.dll
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/bin/Debug/net8.0/runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcp.EDE68F11.Up2Date
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcpServer.dll
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/refint/VaultMcpServer.dll
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcpServer.pdb
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/VaultMcpServer.genruntimeconfig.cache
/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/Debug/net8.0/ref/VaultMcpServer.dll
