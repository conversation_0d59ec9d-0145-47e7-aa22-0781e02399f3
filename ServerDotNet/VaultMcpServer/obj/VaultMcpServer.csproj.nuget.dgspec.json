{"format": 1, "restore": {"/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/VaultMcpServer.csproj": {}}, "projects": {"/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/VaultMcpServer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/VaultMcpServer.csproj", "projectName": "VaultMcpServer", "projectPath": "/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/VaultMcpServer.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/mcp_vault/ServerDotNet/VaultMcpServer/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}